// Taken from https://github.com/tsconfig/bases/blob/main/bases/node16.json (Centralized Recommendations for TSConfig bases)
{
  "compilerOptions": {
    "lib": ["es2021"],
    "module": "commonjs",
    "target": "es2021",

    "allowJs": true,
    "outDir": "./dist",
    "rootDir": "./",

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*", "submodules/ryvyl-commons/types/**/*"],
  "exclude": ["node_modules", "**/*.spec.ts"]
}
