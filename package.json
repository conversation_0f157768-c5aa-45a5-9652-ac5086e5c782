{"name": "ryvyl-il-service", "version": "1.0.0", "author": "Encorp.io", "repository": {"type": "git", "url": "git+https://github.com/encorp-io/ryvyl-express"}, "scripts": {"preinstall": "node checkNodeVersion", "prestart": "node checkNodeVersion", "start": "ts-node-dev --respawn --transpile-only --exit-child --clear ./src/index.ts", "build": "tsc --sourcemap", "prod": "node ./dist/src/index.js", "test": "jest --verbose --setupFiles dotenv/config", "prepare": "husky install", "format": "pretty-quick --staged", "check-lint": "eslint . --ext ts --ext tsx --ext js --ext jsx", "update-ryvyl-commons": "git submodule update --init --recursive --remote submodules/ryvyl-commons", "migrate": "npx migrate-mongo up"}, "dependencies": {"@octokit/rest": "^19.0.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.6", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "argon2": "^0.40.1", "axios": "^1.6.8", "base64url": "^3.0.1", "bcrypt": "^5.1.1", "common": "file:submodules/ryvyl-commons", "cors": "2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "dotenv": "10.0.0", "express": "^4.21.1", "handlebars": "^4.7.8", "i18n-iso-countries": "^7.13.0", "joi": "^17.13.3", "jose": "^5.2.2", "jsonwebtoken": "^9.0.2", "log4js": "^6.9.1", "mongoose": "^8.2.3", "mongoose-paginate-v2": "^1.8.2", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "node-jose": "^2.2.0", "openai": "^4.56.1", "oracledb": "^6.3.0", "os-utils": "^0.0.14", "path": "^0.12.7", "puppeteer": "^23.10.2", "qrcode": "^1.5.4", "request": "^2.88.2", "speakeasy": "^2.0.0", "uuid": "^10.0.0", "winston": "^3.3.3", "winston-loggly-bulk": "^3.3.0"}, "devDependencies": {"@babel/preset-env": "^7.24.4", "@babel/register": "^7.23.7", "@types/cors": "2.8.12", "@types/crypto-js": "^4.2.2", "@types/express": "^5.0.1", "@types/handlebars": "^4.1.0", "@types/jest": "^29.5.12", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.11", "@types/node-jose": "^1.1.13", "@types/oracledb": "^6.5.1", "@types/os-utils": "^0.0.1", "@types/puppeteer": "^7.0.4", "@types/request": "^2.48.12", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/winston-loggly-bulk": "^3.0.6", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-google": "0.14.0", "eslint-plugin-jsdoc": "37.0.3", "husky": "^9.0.11", "jest": "^29.7.0", "pretty-quick": "^4.0.0", "supertest": "^6.3.4", "ts-jest": "^29.2.4", "ts-node-dev": "1.1.8", "typescript": "^5.5.4"}}