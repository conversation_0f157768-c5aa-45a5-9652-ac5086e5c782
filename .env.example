GITHUB_TOKEN='example-token'
GITHUB_REPO_OWNER='example-owner'
GITHUB_REPO='example-repo'
GITHUB_DEV_TRACKED_BRANCH='staging'
GITHUB_DEFAULT_TRACKED_BRANCH='production'
GITHUB_FETCHED_COMMITS_PER_PAGE=100

LOGGER_TOKEN=

# Encryption key for sensitive data (like 2FA secrets)
ENCRYPTION_KEY=<strong-random-key-at-least-32-characters>

MONGODB_HOST=
MONGODB_PORT=
MONGODB_DATABASE_NAME=
MONGODB_USERNAME=
MONGODB_PASSWORD=
MONGODB_URI=
CA_CERT=

PORT=
PAXUM_TO_IL_PORT=<port for paxum services>

DEPLOYMENT=DEV

HEALTH_CHECK_LOAD_INTERVAL=

ORACLE_USER=aml_brcons2
ORACLE_PASSWORD=brpass2
ORACLE_CONNECTING_STRING=***********:1521/LOCAL11

SUMSUB_BASE_URL=<url string>
SUMSUB_APP_TOKEN=<app token generated from sumsub>
SUMSUB_SECRET_KEY=<secrect key generated from sumsub>
SUMSUB_AML_TRANSACTION_VERIFICATION_LEVEL=<level which new applicant need to pass for authentication (for now is empty)>
SUMSUB_AML_TRANSACTION_WEBHOOK_SECRET_KEY=<webhook secret key>

OPENAI_API_KEY=<openai_key>

# VISA VMSS
VISA_VMSS_API_PAYOUT=<Base URL for VISA VMSS>
VISA_VMSS_ACQUIRING_BID=<Acquiring Bid ID for the Ryvyl in the VISA VMSS>
VISA_VMSS_API_KEY_PATH=<Path to the private key for the VISA VMSS>
VISA_VMSS_API_CERT_PATH=<Path to the certificate for the VISA VMSS>
VISA_VMSS_API_CA_PATH=<Path to the CA for the VISA VMSS>
VISA_VMSS_API_USERNAME=<Username for the VISA VMSS>
VISA_VMSS_API_PASSWORD=<Password for the VISA VMSS>
VISA_VMSS_API_MLE_KEY_ID=<MLE Key ID for the VISA VMSS>
VISA_VMSS_API_MLE_PUBLIC_KEY_PATH=<Path to the public key for (message encryption) for the VISA VMSS>
VISA_VMSS_API_MLE_PRIVATE_KEY_PATH=<Path to the private key for (message encryption) for the VISA VMSS>
VISA_VMSS_API_HOSTNAME=<Hostname for the VISA VMSS>

#Redis 
REDIS_URL=<your_redis_url>
REDIS_PORT=<your_redis_port>
REDIS_PASSWORD=<your_redis_password>

JWT_EXPARATION_TIME=<Exparation time on a token>