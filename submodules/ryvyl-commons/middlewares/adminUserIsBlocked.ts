import { Request, Response, NextFunction } from 'express';
import { getRyvylAdminIsBlocked } from '../services/redisService';

async function isBlockedMiddleware(request: Request, response: Response, next: NextFunction) {
  const userId = request?.user?.userId;
  if (!userId) {
    response.status(401).json({ message: 'Unauthorized' });
    return;
  }
  const isBlocked = await getRyvylAdminIsBlocked(userId);

  if (isBlocked === 'true') {
    response.status(403).json({ message: 'User is blocked' });
    return;
  } else if (isBlocked === 'false') {
    next();
  } else {
    response.status(403).json({ message: 'User status is unknown' });
    return;
  }
}
export default isBlockedMiddleware;
