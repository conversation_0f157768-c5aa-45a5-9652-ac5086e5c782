import { Request, Response, NextFunction } from 'express';
import { Schema } from 'joi';

/**
 * Middleware to validate request body against a given Joi schema.
 * @param schema - Joi validation schema
 */
const validateRequest = (schema: Schema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body, { abortEarly: false });

    if (error) {
      const errorMessages = error.details.map((err) => {
        const additionalContext = err?.context?.message;

        if (additionalContext) {
          return additionalContext;
        }

        return err.message.replace(/"/g, '');
      });
      res.status(400).json({ messages: errorMessages });
      return;
    }

    next();
  };
};

export default validateRequest;
