import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

import logger from '../services/loggerService';
import { verifyToken } from '../services/authService';
import { IS_DEV } from '../config';
import { CustomRequestWithUser } from '../types/express';

/**
 * Middleware to validate the authentication JWT bearer token from the request.
 */
export function authenticateJWT(req: CustomRequestWithUser, res: Response, next: NextFunction): void {
  try {
    let token;

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ message: 'Unauthorized!' });
      return;
    }

    token = authHeader.split(' ')[1];

    if (!token) {
      res.status(401).json({ message: 'Unauthorized: No token provided' });
      return;
    }

    req.user = verifyToken(token);
    next();
  } catch (err: any) {
    logger.error(`Token verification failed: ${err.message}`);
    if (err instanceof jwt.TokenExpiredError) {
      // Expired token
      res.status(401).json({ message: 'Token has expired!' });
    } else if (err instanceof jwt.JsonWebTokenError) {
      // Invalid token
      res.status(401).json({ message: 'Invalid token!' });
    } else {
      // Other errors
      res.status(500).json({ message: 'Internal server error!' });
    }

    return;
  }
}
