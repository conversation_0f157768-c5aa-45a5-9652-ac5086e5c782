import OpenAI from 'openai';
import logger, { AIClassifierLogger } from '../services/loggerService';

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  organization: process.env.OPENAI_API_ORGANIZATION,
  project: process.env.OPENAI_API_PROJECT
});

export enum TypeOfCustomer {
  Person = 'Person',
  Company = 'Company',
  Unknown = 'Unknown'
}

export async function classifyCustomerType(name: string): Promise<TypeOfCustomer> {
  const prompt = `Is "${name}" a name of a person or a company? Provide a one-word answer: "Person" or "Company".`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // Use the model of your choice
      messages: [
        { role: 'system', content: 'You are a highly accurate text classifier.' },
        { role: 'user', content: prompt }
      ],
      max_tokens: 5,
      temperature: 0.0 // Ensures deterministic responses
    });

    const result = response.choices[0].message.content?.trim() ?? 'Unknown';
    let typeOfCustomer = (result as TypeOfCustomer) ?? TypeOfCustomer.Unknown;
    AIClassifierLogger.log('info', `Result ${name} is ${result}`);
    return Promise.resolve(typeOfCustomer);
  } catch (error) {
    logger.error('Error identifying name type:', error);
    throw new Error('Could not classify the name');
  }
}
