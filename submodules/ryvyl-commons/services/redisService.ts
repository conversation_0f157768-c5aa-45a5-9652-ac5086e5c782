import { createClient } from 'redis';
import logger from './loggerService';
import { REDIS } from '../config';

// Redis client configuration
const redisClient = createClient({
  socket: {
    host: REDIS.URL,
    port: Number(REDIS.PORT)
  },
  password: REDIS.PASSWORD
});

redisClient.on('connect', () => {
  logger.info('🔌 Connected to Redis');
});

redisClient.on('error', (err: any) => {
  logger.error(`❌ Redis connection error: ${err.message}`);
});

// Set the status isBlocked to adminId
export function setRyvylAdminIsBlocked(adminId: string, isBlocked: boolean) {
  try {
    redisClient.set(`ryvyl-admin-isBlocked:${adminId}`, String(isBlocked), {
      EX: 7200 // 2 hours in seconds
    });
  } catch (error: any) {
    logger.error(`Failed to set status isBlocked to adminId: ${adminId} error: ${error.message}`);
  }
}

// Get the status isBlocked from adminId
export function getRyvylAdminIsBlocked(adminId: string) {
  return redisClient.get(`ryvyl-admin-isBlocked:${adminId}`);
}

export default redisClient;
