import { Kafka, Consumer, Producer, EachMessagePayload } from 'kafkajs';

import { KAFKA } from '../config';
import logger from './loggerService';

export class KafkaService {
  private kafka: Kafka;
  private producer: Producer;
  private consumers: Map<string, Consumer> = new Map();
  private isConnected: boolean = false;

  constructor() {
    this.kafka = new Kafka({
      brokers: KAFKA.BROKERS
    });

    this.producer = this.kafka.producer();
  }

  // 🔹 Connect Kafka Producer
  async connect(): Promise<void> {
    if (this.isConnected) return;

    try {
      await this.producer.connect();
      this.isConnected = true;
      logger.info('✅ Connected to Kafka Producer');
    } catch (error: any) {
      logger.error(`❌ Error connecting to Kafka Producer: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Disconnect Kafka
  async disconnect(): Promise<void> {
    if (!this.isConnected) return;

    try {
      // Disconnect all consumers
      for (const [name, consumer] of this.consumers) {
        await consumer.disconnect();
        logger.info(`✅ Disconnected consumer: ${name}`);
      }

      await this.producer.disconnect();
      this.isConnected = false;
      logger.info('✅ Disconnected from Kafka');
    } catch (error: any) {
      logger.error(`❌ Error disconnecting from Kafka: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Ensure the Topic Exists (and Create if not)
  private async ensureTopicExists(topic: string): Promise<void> {
    const admin = this.kafka.admin();

    await admin.connect();
    const topics = await admin.listTopics();

    // Check if the topic exists
    if (!topics.includes(topic)) {
      logger.info(`Topic "${topic}" does not exist. Creating it now.`);
      await admin.createTopics({
        topics: [{ topic, numPartitions: 3, replicationFactor: 1 }]
      });
      logger.info(`Topic "${topic}" created successfully.`);
    }

    // Refresh metadata by listing topics again
    // It ensures that Kafka consumer can recognize the newly created topics
    const updatedTopics = await admin.listTopics();
    logger.info(`Updated topic list: ${updatedTopics}`);

    await admin.disconnect();
  }

  // 🔹 Subscribe to Kafka Topics with a dedicated consumer
  async subscribe(
    topics: string[],
    fromBeginning: boolean,
    callback: (topic: string, message: any) => Promise<void>,
    consumerName: string = 'default'
  ): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Kafka is not connected');
    }

    try {
      // Create a new consumer for this subscription if it doesn't exist
      if (!this.consumers.has(consumerName)) {
        const consumer = this.kafka.consumer({
          groupId: `${KAFKA.GROUP_ID}-${consumerName}`
        });
        await consumer.connect();
        this.consumers.set(consumerName, consumer);
        logger.info(`✅ Created and connected consumer: ${consumerName}`);
      }

      const consumer = this.consumers.get(consumerName)!;

      // Ensure all topics exist
      for (const topic of topics) {
        await this.ensureTopicExists(topic);
      }

      // Subscribe the consumer to the topics
      await consumer.subscribe({ topics, fromBeginning });

      // Start consuming messages
      await consumer.run({
        eachMessage: async ({ topic, message }: EachMessagePayload) => {
          try {
            if (!message.value) return;

            const messageValue = message.value.toString();
            let parsedMessage;
            try {
              parsedMessage = JSON.parse(messageValue);
            } catch (err) {
              parsedMessage = messageValue;
            }

            await callback(topic, parsedMessage);
          } catch (err: any) {
            logger.error(`❌ Error processing message from topic: "${topic}", error: ${err.message}`);
          }
        }
      });

      logger.info(`✅ Consumer ${consumerName} subscribed to Kafka topics: ${topics}`);
    } catch (error: any) {
      logger.error(`❌ Error subscribing consumer ${consumerName} to Kafka topics: ${topics}, error: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Publish Messages to Kafka Topics
  async publish(topic: string, key: string, message: any): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Kafka is not connected');
    }

    try {
      await this.producer.send({
        topic,
        messages: [{ key, value: JSON.stringify(message) }]
      });
    } catch (error: any) {
      logger.error(`❌ Error sending message to topic: "${topic}", error: ${error.message}`);
      throw error;
    }
  }
}

// ✅ Export the instance to use Kafka for both publishing and subscribing
export const kafkaService = new KafkaService();
