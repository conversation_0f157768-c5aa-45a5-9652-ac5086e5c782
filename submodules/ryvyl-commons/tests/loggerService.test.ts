import winston from 'winston';

describe('Logger Service', () => {
  let logger: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Import logger after environment variables are set
    logger = require('../services/loggerService').default;
  });

  it('should log an info message correctly', () => {
    const spy = jest.spyOn(logger, 'info');
    logger.info('Test message');

    expect(spy).toHaveBeenCalledWith('Test message');
  });

  it('should log an error message correctly', () => {
    const spy = jest.spyOn(logger, 'error');
    logger.error('Something went wrong!');

    expect(spy).toHaveBeenCalledWith('Something went wrong!');
  });

  it('should use the correct transports', () => {
    expect(logger.transports.length).toBeGreaterThan(0);
  });

  it('should not include Loggly transport in non-production', () => {
    const testLogger = winston.createLogger({
      transports: [new winston.transports.Console()]
    });

    expect(testLogger.transports.some((t) => t instanceof winston.transports.Console)).toBe(true);
    expect(testLogger.transports.some((t) => t instanceof winston.transports.Http)).toBe(false);
  });
});
