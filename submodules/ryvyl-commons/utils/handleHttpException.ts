import { Response } from 'express';

import HttpException from '../classes/HttpException';

function handleHttpException(error: any, res: Response) {
  let status = 500;
  let message = 'Something went wrong, please try again!';
  if (error instanceof HttpException) {
    status = error.status;
    message = error.message;
  }

  res.status(status).send({
    message: message
  });
  return 
}

export default handleHttpException;
