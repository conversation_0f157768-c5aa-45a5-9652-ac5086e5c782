import { Request } from 'express';

/**
 * @param value
 */
export function normalizeStringNewlines(value: string): string {
  return value.replace(/\\n/g, '\n');
}

/**
 * Method used to get the IP address of the client
 * @param req the express request object
 * @returns the IP Address of the client
 */
export const getClientIp = (req: Request) => {
  let clientIp = (req.headers['x-real-ip'] as string) || (req.headers['x-forwarded-for'] as string)?.split(',')[0] || req.ip;

  if (clientIp?.startsWith('::ffff:')) {
    clientIp = clientIp.substring(7);
  }

  return clientIp;
};
