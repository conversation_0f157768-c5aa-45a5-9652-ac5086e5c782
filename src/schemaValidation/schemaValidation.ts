import Joi from 'joi';
import { CreateInboundTransaction, TransactionByIBAN } from '../interfaces/routesInterface';

export const transactionByIBANSearchSchema = Joi.object<TransactionByIBAN>({
  dateStart: Joi.date().iso().required().messages({
    'date.base': 'dateStart must be a valid date',
    'any.required': 'dateStart is required'
  }),
  dateEnd: Joi.date()
    .iso()
    .required()
    .when('dateStart', { is: Joi.exist(), then: Joi.date().min(Joi.ref('dateStart')) })
    .messages({
      'date.base': 'dateEnd must be a valid date',
      'any.required': 'dateEnd is required',
      'date.min': 'dateEnd must be after or equal to dateStart'
    }),
  iban: Joi.string().length(22).required()
});

export const schemaProcessInboundTransaction = Joi.object<CreateInboundTransaction>({
  valueDate: Joi.date().iso().required().messages({
    'date.base': 'valueDate must be a valid date',
    'any.required': 'valueDate is required',
    'date.format': 'valueDate must be in ISO 8601 format'
  }),
  referenceNumber: Joi.string().required(),
  transactionDate: Joi.date().iso().required().messages({
    'date.base': 'transactionDate must be a valid date',
    'any.required': 'transactionDate is required',
    'date.format': 'transactionDate must be in ISO 8601 format'
  }),
  amount: Joi.alternatives()
    .try(
      Joi.string()
        .pattern(/^\d+([,.]\d{2})?$/, { name: 'decimal numbers' })
        .messages({
          'string.pattern.name':
            'Amount must be a valid number with two decimal places, using either a period or a comma as a decimal separator.'
        }),
      Joi.number().precision(2) // Allows numbers with up to 2 decimal places
    )
    .required(),
  currency: Joi.string().length(3).required(),
  beneficiaryNameAndAddress: Joi.string().required().min(1).max(600),
  beneficiaryBic: Joi.string().required().min(1).max(20),
  beneficiaryAccountNumber: Joi.string().required().min(10).max(50),
  senderName: Joi.string().required().min(1).max(100),
  senderAddress: Joi.string().required().min(1).max(500),
  senderBic: Joi.string().required().min(1).max(20),
  senderAccountNumber: Joi.string().required().min(10).max(50),
  senderBankName: Joi.string().required().min(1).max(100),
  senderBankCountry: Joi.string().required().length(2),
  reason: Joi.string().required().min(1).max(600),
  direction: Joi.number().required().min(1),
  idPay: Joi.number().required()
});
