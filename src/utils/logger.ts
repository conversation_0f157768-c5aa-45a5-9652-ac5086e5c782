import winston from 'winston';
import { getLoggerConfig } from '../../submodules/ryvyl-commons/services/loggerService';

export const amlLogger = winston.createLogger(getLoggerConfig('AML'));
export const visaLogger = winston.createLogger(getLoggerConfig('Visa'));
export const visaVmssLogger = winston.createLogger(getLoggerConfig('Visa-VMSS'));
export const AIClassifierLogger = winston.createLogger(getLoggerConfig('AIClassifier'));
export const jotFormLogger = winston.createLogger(getLoggerConfig('Jotform'));
export const txLogger = winston.createLogger(getLoggerConfig('TechnoXander'));
export const transactionLogger = winston.createLogger(getLoggerConfig('Transaction'));
export const webhookLogger = winston.createLogger(getLoggerConfig('Webhook'));
