import express from 'express';
import { verifyEntityValidity } from '../services/technoXanderService';
import {
  ErrorVerificationResponse,
  NameVerificationRequest,
  ResponderResponse,
  RyvylCopRequest
} from '../interfaces/technoXander';
import { TypeOfCustomer } from '../services/aiCustomerClassifier';
import { getCopClient } from '../services/mongoService';
import { v4 as uuidv4 } from 'uuid';
import { txLogger } from '../utils/logger';

export async function verifyClient(req: express.Request, res: express.Response): Promise<void> {
  const payload = req.body as RyvylCopRequest;

  if (!payload || !payload.name || !payload.accountNumber || !payload.accountType) {
    txLogger.error('Name, IBAN and account type are required');
    res.status(400).json({ match: false, message: 'Name, IBAN and account type are required' });
    return;
  }

  try {
    if (payload.accountNumber.length > 14) payload.accountNumber.slice(-14);
    const result = await verifyEntityValidity(payload);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ match: false, message: error.message });
  }
  txLogger.info(`[${payload.accountNumber}] - client verification done`);
}

export async function clientLookup(req: express.Request, res: express.Response): Promise<void> {
  const payload = req.body as NameVerificationRequest;

  if (!payload) {
    const errorResponse: ErrorVerificationResponse = {
      Code: '400',
      Message: 'Invalid payload',
      Errors: [
        {
          ErrorCode: '400',
          Message: 'Payload not provided'
        }
      ]
    };
    txLogger.error(`Error ${errorResponse.Code} - ${errorResponse.Message}`);
    res.status(400).json(errorResponse);
    return;
  }

  const existingClient = await getCopClient(payload.Data.Identification);

  if (!existingClient) {
    txLogger.info(`[${payload.Data.Identification}] - Client not found in database`);
  }

  const response: ResponderResponse = {
    SchemeName: 'SortCodeAccountNumber',
    AccountType: existingClient ? (existingClient.type === TypeOfCustomer.Person ? 'Personal' : 'Business') : '',
    Identification: existingClient ? existingClient.iban : payload.Data.Identification,
    Name: existingClient ? existingClient.name : 'Account not found in database',
    AccountSwitched: existingClient ? 'N' : '',
    OptedOut: existingClient ? 'N' : '',
    AccountSupported: existingClient ? 'Y' : '',
    AccountNickname: ''
  };

  res.status(200).json(response);
}
