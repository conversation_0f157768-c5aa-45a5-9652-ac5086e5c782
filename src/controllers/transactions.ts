import mongoose from 'mongoose';
import dayjs from 'dayjs';

import VisaTransaction from '../models/mongo/visaTransactionSchema';
import LnTransaction from '../models/mongo/lnTransactionSchema';
import express from 'express';
import { transactionLogger, visaLogger } from '../utils/logger';
import VisaInvalidTx, { VisaInvalidTxDoc } from '../models/mongo/visaInvalidTxSchema';
import { cancelVisaTransaction, sendToVisaTransaction, validatePayout } from '../services/visaService';
import { insertInvalidTransaction, insertVisaTransactions, resolveInvalidTransaction } from '../services/mongoService';
import { getForVisaTransaction, insertResultsDB } from '../services/oracleService';
import { VisaResult } from '../interfaces/visaResult';
import { Transaction } from '../interfaces/oracleTransactionInterface';
import * as VisaType from '../types/visaTypes';
import {
  getCompletedTransactionsByIdOrNumber,
  getCompletedTransactionsByUniqueId
} from '../services/transactionService';
import {
  generateProofOfPaymentHTML,
  generateProofOfPaymentPDFFromHTML,
  ProofOfPaymentData
} from '../services/pdfService';

import logger from '../../submodules/ryvyl-commons/services/loggerService';
import { startManualAMLCheck } from '../services/cronService';

export async function getVisaTransactions(req: express.Request, res: express.Response): Promise<void> {
  const page = parseInt(req.query.page as string) ?? 1;
  const limit = parseInt(req.query.limit as string);
  const searchQuery = (req.query.search as string) ?? '';
  const uniqueIdString = (req.query.uniqueId as string) ?? '';
  const uniqueIdNumber = Number(uniqueIdString);
  const sortBy = (req.query.sortBy as string) || 'updatedAt'; // default sorting field
  const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1; // sorting order: ascending (asc) or descending (desc)
  const startDate = req.query.startDate as string;
  const endDate = req.query.endDate as string;

  // List of valid sortable fields from the schema
  let validSortFields = Object.keys(VisaTransaction.schema.obj);
  validSortFields = [...validSortFields, 'createdAt', 'updatedAt'];

  // Check if the provided sortBy field is valid
  const sortField = validSortFields.includes(sortBy) ? sortBy : 'updatedAt';

  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    projection: {},
    select: '-_id -__v '
  };

  let filter: any = {};
  if (searchQuery) {
    const schemaKeys = Object.keys(VisaTransaction.schema.obj);

    // Filter only fields with string data type because only they can be search with regex
    const searchableFields = schemaKeys.filter((key) => {
      const field = VisaTransaction.schema.path(key);
      return field instanceof mongoose.Schema.Types.String;
    });

    const regex = new RegExp(searchQuery, 'i');
    const additionalFilters = {
      $or: searchableFields.map((key) => {
        return {
          [key]: { $regex: regex }
        };
      })
    };

    filter = { ...filter, ...additionalFilters };
  }

  if (uniqueIdNumber) {
    filter['$and'] = [{ uniqueId: uniqueIdNumber }];
  }

  if (startDate && endDate) {
    const start = dayjs(startDate, 'DD/MM/YYYY').startOf('day').toDate();
    const end = dayjs(endDate, 'DD/MM/YYYY').endOf('day').toDate();

    // Check if start date is before or equal to end date
    if (dayjs(start).isBefore(end) || dayjs(start).isSame(end)) {
      filter.createdAt = {
        $gte: start,
        $lte: end
      };
    }
  }

  try {
    const result = await VisaTransaction.paginate(filter, options);
    res.json(result);
  } catch (error) {
    logger.error(`Error fetching Visa transactions: `, error);
    res.status(500).json({ message: 'An error occurred while fetching Visa transactions' });
  }
}

export async function getAMLTransactions(req: express.Request, res: express.Response): Promise<void> {
  const page = parseInt(req.query.page as string) ?? 1;
  const limit = parseInt(req.query.limit as string) ?? 100;
  const searchQuery = (req.query.search as string) ?? '';
  const transactionType = (req.query.transactionType as string) ?? '';
  const uniqueIdString = (req.query.uniqueId as string) ?? '';
  const uniqueIdNumber = Number(uniqueIdString);
  const sortBy = (req.query.sortBy as string) || 'updatedAt'; // default sorting field
  const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1; // sorting order: ascending (asc) or descending (desc)
  const startDate = req.query.startDate as string;
  const endDate = req.query.endDate as string;
  const downloadAll = req.query.downloadAll === 'true';
  const status = req.query.status as 'T' | 'F' | null;

  // List of valid sortable fields from the schema
  let validSortFields = Object.keys(LnTransaction.schema.obj);
  validSortFields = [...validSortFields, 'createdAt', 'updatedAt'];

  // Check if the provided sortBy field is valid
  const sortField = validSortFields.includes(sortBy) ? sortBy : 'updatedAt';

  let options: any = {
    sort: { [sortField]: sortOrder },
    projection: {},
    select: '-_id -__v '
  };

  // If downloadAll is true, remove pagination settings
  if (!downloadAll) {
    options.page = page;
    options.limit = limit;
  }

  let filter: any = {};

  if (status && (status === 'T' || status === 'F')) {
    filter.status = status;
  }

  if (transactionType && transactionType.toUpperCase() != 'ALL') {
    filter.transactionType = transactionType.toUpperCase();
  }
  if (searchQuery) {
    const schemaKeys = Object.keys(LnTransaction.schema.obj);

    // Filter only fields with string data type because only they can be search with regex
    const searchableFields = schemaKeys.filter((key) => {
      const field = LnTransaction.schema.path(key);
      return field instanceof mongoose.Schema.Types.String;
    });

    const regex = new RegExp(searchQuery, 'i');
    const additionalFilters = {
      $or: searchableFields.map((key) => {
        return {
          [key]: { $regex: regex }
        };
      })
    };

    filter = { ...filter, ...additionalFilters };
  }

  if (uniqueIdNumber) {
    filter['$and'] = [{ uniqueId: uniqueIdNumber }];
  }

  if (startDate && endDate) {
    const start = dayjs(startDate, 'DD/MM/YYYY').startOf('day').toDate();
    const end = dayjs(endDate, 'DD/MM/YYYY').endOf('day').toDate();

    // Check if start date is before or equal to end date
    if (dayjs(start).isBefore(end) || dayjs(start).isSame(end)) {
      filter.createdAt = {
        $gte: start,
        $lte: end
      };
    }
  }

  try {
    const result = downloadAll
      ? { data: await LnTransaction.find(filter, options.select).sort(options.sort).exec() }
      : await LnTransaction.paginate(filter, options);

    res.json(result);
  } catch (error) {
    logger.error(`Error fetching AML transactions `, error);
    res.status(500).json({ message: 'An error occurred while fetching AML transactions' });
  }
}

export async function resendVisaTransaction(req: express.Request, res: express.Response): Promise<void> {
  try {
    const data = req.body;
    const payload: VisaType.Payload = data.payload;

    const referenceId = payload.transactionDetail.clientReferenceId ?? '';
    const existingTransaction = await VisaInvalidTx.findOne({
      referenceId: referenceId
    });

    // Step 1 Check if transaction exist in MongoDB
    if (existingTransaction) {
      let validatedPayout = await validatePayout(payload);
      // Step 2 Validate the new payout
      if (validatedPayout) {
        if (validatedPayout.validationResultCode === 'INVALID' || validatedPayout.validationResultCode === 'ERROR') {
          //@ts-ignore
          visaLogger.debug(`Visa transaction with reference id ${referenceId} has invalid payout`);
          // Step 3a: If INVALID update the entity in MongoDB
          let message = '';
          if (validatedPayout.validationResultCode === 'INVALID') {
            message = validatedPayout
              .validationDetails!![0].details.map((detail) => `${detail.message} - ${detail.location}`)
              .join('\r\n');
          } else {
            message = JSON.stringify(validatedPayout.validationDetails!![0]);
          }

          payload.transactionDetail.clientReferenceId = referenceId;
          const mongoDBResult = await insertInvalidTransaction(
            referenceId,
            message,
            payload,
            existingTransaction.uniqueId
          );
          if (!mongoDBResult) {
            visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
            res.status(500).json({ message: 'An error occurred while resending Visa transaction' });
          }
          res.status(400).json({ message: message });
        } else {
          payload.transactionDetail.clientReferenceId = referenceId;

          let result: any = {};
          // Step 3b: Send the payout to Visa
          await sendToVisaTransaction(payload)
            .then((resultTransaction: VisaType.SendTransactionResponse) => {
              const decryptedStatus = resultTransaction.data.transactionDetail?.status;
              let status;
              switch (resultTransaction.statusCode) {
                case 202:
                  status = 'P';
                  break;

                case 200:
                  status = decryptedStatus === 'PAYMENT_RECEIVED' ? 'T' : 'P';
                  break;

                default:
                  status = 'F';
                  break;
              }

              result = {
                status: status,
                visaStatus: decryptedStatus?.replace('_', ' ') ?? '',
                visaMsg: ''
              };
            })
            .catch((error: Error) => {
              visaLogger.error(error.message);
              result = {
                status: 400,
                message: error.message
              };
            });

          if (result.status == 400) {
            res.status(404).json({ message: 'error.message' });
            return;
          }

          if (result) {
            const oracleRecord = await getForVisaTransaction(referenceId);

            if (oracleRecord.length > 0) {
              const transaction = oracleRecord[0] as Transaction;
              const visaResult: VisaResult = {
                uniqueId: transaction.UNIQUE_ID,
                referenceId: referenceId,
                referenceNumber: transaction.REFERENCE_NUMBER,
                mode_check: 2,
                status: result.status,
                visaStatus: result.visaStatus,
                visaMsg: result.visaMsg
              };
              // Step 4: Saving the results in Oracle DB (AML_RESULT_TRANSACTIONS table)
              const oracleResult = await insertResultsDB(visaResult, 2);
              if (!oracleResult) {
                visaLogger.debug('FAILED_INSERT_ORACLEDB');
                res.status(500).json({ message: 'An error occurred while resending Visa transaction' });
              }

              // Step 5: Saving the results in MongoDB (ILDB - visa transactions) and marked the invalid transaction as resolved (ILDB visa invalid txs)
              const insertResult = await insertVisaTransactions(visaResult);
              const resolveInvalidResult = await resolveInvalidTransaction(referenceId);
              await insertInvalidTransaction(referenceId, '', payload, existingTransaction.uniqueId);

              if (!(insertResult || resolveInvalidResult)) {
                visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
                res.status(500).json({ message: 'An error occurred while resending Visa transaction' });
              }
              visaLogger.debug(`Transaction with reference id ${referenceId} successfully sent to Visa`);
              res.status(200).json({ message: 'Transaction resent successfully' });
            } else {
              res.status(400).json({ message: 'Transaction is not found in the Oracle database!' });
            }
          }
        }
      }
    } else res.status(404).json({ message: 'Transaction not found' });
  } catch (error) {
    logger.error(`Error resending Visa transaction `, error);
    res.status(500).json({ message: 'An error occurred while resending Visa transaction' });
  }
}

export async function getVisaInvalidTransactions(req: express.Request, res: express.Response): Promise<void> {
  const pageParam = (req.query.page as string) ?? 1;
  const limitParam = (req.query.limit as string) ?? 1000;
  const page = parseInt(pageParam);
  const limit = parseInt(limitParam);
  const sortBy = (req.query.sortBy as string) || 'updatedAt'; // default sorting field
  const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1; // sorting order: ascending (asc) or descending (desc)

  // List of valid sortable fields from the schema
  let validSortFields = Object.keys(VisaInvalidTx.schema.obj);
  validSortFields = [...validSortFields, 'createdAt', 'updatedAt'];

  // Check if the provided sortBy field is valid
  const sortField = validSortFields.includes(sortBy) ? sortBy : 'updatedAt';

  try {
    const query = { resolved: false };
    const options = {
      page,
      limit,
      select: `
       -_id
       -__v -payload._id
       -payload.senderDetail._id
       -payload.senderDetail.address._id
       -payload.recipientDetail._id 
       -payload.recipientDetail.address._id
       -payload.recipientDetail.bank._id 
       -payload.transactionDetail._id
       `,
      sort: { [sortField]: sortOrder }
    };

    const visaInvalidTransactions = await VisaInvalidTx.paginate(query, options);

    visaInvalidTransactions.docs.forEach((transaction: VisaInvalidTxDoc) => {
      //@ts-ignore
      delete transaction.payload.recipientDetail?.identificationList?.at(0)?._id;
      //@ts-ignore
      delete transaction.payload.recipientDetail?.additionalData?.at(0)?._id;
    });

    res.json(visaInvalidTransactions);
  } catch (error) {
    logger.error(`Error fetching invalid Visa transactions: `, error);
    res.status(500).json({ message: 'An error occurred while fetching invalid Visa transactions' });
  }
}

export async function getVisaInvalidTransaction(req: express.Request, res: express.Response): Promise<void> {
  const referenceId = req.params.referenceId;

  if (!referenceId) {
    res.status(400).json({ message: 'referenceId is required!' });
  }

  try {
    const pipeline = [
      { $match: { referenceId: referenceId, resolved: false } },
      {
        $project: {
          _id: 0,
          __v: 0,
          createdAt: 0,
          updatedAt: 0,
          'payload._id': 0,
          'payload.senderDetail._id': 0,
          'payload.senderDetail.address._id': 0,
          'payload.recipientDetail._id': 0,
          'payload.recipientDetail.address._id': 0,
          'payload.recipientDetail.bank._id': 0,
          'payload.transactionDetail._id': 0
        }
      }
    ];
    const visaInvalidTransactions = await VisaInvalidTx.aggregate(pipeline).exec();

    visaInvalidTransactions.forEach((transaction) => {
      delete transaction.payload.recipientDetail?.identificationList?.at(0)?._id;
      delete transaction.payload.recipientDetail?.additionalData?.at(0)?._id;
    });

    res.json({ transaction: visaInvalidTransactions[0] });
  } catch (error) {
    logger.error(`Error fetching invalid Visa transaction: `, error);
    res.status(500).json({ message: 'An error occurred while fetching invalid Visa transactions' });
  }
}

export async function rejectVisaInvalidTransaction(req: express.Request, res: express.Response): Promise<void> {
  try {
    const data = req.body;
    const referenceId: string = data.referenceId;

    const existingTransaction = await VisaInvalidTx.findOne({
      referenceId: referenceId
    });

    // Step 1: Check for existing transaction in MongoDB
    if (existingTransaction) {
      // Step 2: Check for existing transaction in Oracle database
      const oracleRecord = await getForVisaTransaction(referenceId);
      if (oracleRecord.length > 0) {
        const transaction = oracleRecord[0] as Transaction;
        const visaResult: VisaResult = {
          uniqueId: transaction.UNIQUE_ID,
          referenceId: referenceId,
          referenceNumber: transaction.REFERENCE_NUMBER,
          mode_check: 2,
          status: 'F',
          visaStatus: '',
          visaMsg: ''
        };
        const oracleResult = await insertResultsDB(visaResult, 2);
        if (!oracleResult) {
          visaLogger.debug('FAILED_INSERT_ORACLEDB');
          res.status(500).json({ message: 'An error occurred while resending Visa transaction' });
        }

        // Step 3: Saving the results in MongoDB (ILDB - visa transactions) and marked the invalid transaction as resolved (ILDB visa invalid txs)
        const insertResult = await insertVisaTransactions(visaResult);
        const resolveInvalidResult = await resolveInvalidTransaction(referenceId);

        if (!(insertResult || resolveInvalidResult)) {
          visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
          res.status(500).json({ message: 'An error occurred while resending Visa transaction' });
        }
        visaLogger.debug(`Transaction with reference id ${referenceId} was rejected!`);
        res.status(200).json({ message: 'Transaction rejected!' });
      } else res.status(400).json({ message: 'Transaction is not found in the Oracle database!' });
    } else res.status(404).json({ message: 'Transaction not found' });
  } catch (error) {}
}

export async function cancelVisaTransactionRequest(req: express.Request, res: express.Response) {
  const clientReferenceId = req.params.clientReferenceId;
  try {
    const result: VisaType.VisaTransactionResponse = await cancelVisaTransaction(clientReferenceId);
    res.status(result.statusCode).json({ message: 'Transaction successfully send to Visa for cancellation.' });
  } catch (error: any) {
    res
      .status(error.statusCode ? error.statusCode : 400)
      .json({ message: error.message ? error.message : 'Something went wrong please try again!' });
  }
}

export async function getCompletedTransactionsByUniqueIdController(
  req: express.Request,
  res: express.Response
): Promise<void> {
  const uniqueId = req.params.uniqueId;

  // Convert and validate the number
  const numericId = Number(uniqueId);
  if (isNaN(numericId)) {
    res.status(400).send({ message: 'Invalid unique ID. Must be a valid number.' });
    return;
  }
  try {
    const transaction = await getCompletedTransactionsByUniqueId(numericId);
    res.status(200).send({ data: transaction });
  } catch (error) {
    res.status(500).send({ message: 'An error occurred while fetching transactions by unique id' });
  }
}

export async function getCompletedTransactionsByIdOrNumberController(
  req: express.Request,
  res: express.Response
): Promise<void> {
  const transactionId = req.params.transactionId;

  try {
    const transaction = await getCompletedTransactionsByIdOrNumber(transactionId);
    res.status(200).send({ data: transaction });
  } catch (error) {
    res.status(500).send({ message: 'An error occurred while fetching transactions by unique id' });
  }
}

export async function generateProofOfPaymentPDFByUniqueId(req: express.Request, res: express.Response) {
  const uniqueId = req.params.uniqueId;

  // Convert and validate the number
  const numericId = Number(uniqueId);
  if (isNaN(numericId)) {
    res.status(400).send({ message: 'Invalid unique ID. Must be a valid number.' });
    return;
  }

  try {
    const transaction = await getCompletedTransactionsByUniqueId(numericId);

    if (!transaction) {
      res.status(404).send({ message: 'Transaction not found' });
      return;
    }

    const transactionDate = new Date(transaction.TIMESTAMP).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    const transactionType =
      transaction.TRANSACTION_TYPE === 'OT' || transaction.TRANSACTION_TYPE === 'OS' ? 'OUTBOUND' : 'INBOUND';
    const transactionData: ProofOfPaymentData = {
      proofOfPaymentTo: transaction?.CLIENT_NAME,
      transactionStatus: 'Completed',
      transactionType: transactionType,
      transactionId: transaction.REFERENCE_NUMBER,
      transactionDate: transactionDate,
      transactionAmount: transaction.AMOUNT_ORIGINAL_CURRENCY,
      transactionCurrency: transaction.ORIGINAL_CURRENCY,
      beneficiaryNameAndAddress: transaction.BENEFICIARY_NAME_AND_ADDRESS,
      beneficiaryBankName: transaction.BENEFICIARY_BANK_NAME as string,
      beneficiaryBIC: transaction.BENEFICIARY_BIC as string,
      beneficiaryAccountNumber: transaction.BENEFICIARY_ACCOUNT_NUMBER,
      beneficiaryReason: transaction.REASON
    };

    const html = generateProofOfPaymentHTML(transactionData);

    const fileName = `${transactionData.proofOfPaymentTo.trim()}-${transactionData.transactionId}.pdf`;
    // Generate PDF
    const { pdf, filename } = await generateProofOfPaymentPDFFromHTML(html, fileName);

    res.setHeader('name', `attachment;`);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `${fileName}`);
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

    res.send(pdf);
  } catch (error: any) {
    transactionLogger.error(`Failed generating proof of payment error: ${error.message}`);
    res.status(500).json({
      message: 'Failed to generate proof of payment PDF'
    });
  }
}

export async function triggerManualAMLCheck(req: express.Request, res: express.Response): Promise<void> {
  const { fromUniqueId } = req.body;

  if (!fromUniqueId || typeof fromUniqueId !== 'number') {
    res.status(400).json({
      success: false,
      message: 'Invalid fromUniqueId parameter. Please provide a valid number.'
    });
    return;
  }

  try {
    const result = await startManualAMLCheck(fromUniqueId);
    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(409).json(result); // 409 Conflict when cron is active
    }
  } catch (error: any) {
    logger.error(`Error triggering manual AML check: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `An error occurred while triggering manual AML check: ${error.message}`
    });
  }
}
