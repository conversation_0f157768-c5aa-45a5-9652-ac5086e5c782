import { Request, Response } from 'express';
import { verify2FAToken, setup2FASecret } from '../services/twoFactorService';
import getJwtAuthToken from '../helpers/getJwtAuthToken';
import logger from '../../submodules/ryvyl-commons/services/loggerService';
import handleHttpException from '../../submodules/ryvyl-commons/utils/handleHttpException';
/**
 * Verify 2FA token during login
 */
export async function verifyTwoFactorTokenController(req: Request, res: Response): Promise<void> {
  const { code, twoFAToken, encrypted2FASecret } = req.body;
  try {
    // Validate the 2fa token and return the user
    const { user, isValid } = await verify2FAToken(code, twoFAToken, encrypted2FASecret);

    if (!isValid) {
      res.status(401).json({ success: false, message: 'Invalid 2FA token' });
      return;
    }

    // Generate JWT auth token
    const jwtToken = await getJwtAuthToken(user);
    if (encrypted2FASecret) {
      user.twoFactorSecret = encrypted2FASecret;
      await user.save();
    }
    res.status(200).json({ success: true, token: jwtToken, username: user.username });
  } catch (error: any) {
    logger.error(`Error verifying 2FA token: ${error.message}`);
    handleHttpException(error, res);
  }
}

/**
 * Set 2FA token during login
 */
export async function setupTwoFactorTokenController(req: Request, res: Response): Promise<void> {
  const { twoFATempToken } = req.body;
  try {
    // Get the 2FA qr code and the encrypted secret
    const { qrCode, encrypted2FASecret } = await setup2FASecret(twoFATempToken);

    res.status(200).json({ success: true, qrCode: qrCode, encrypted2FASecret: encrypted2FASecret });
  } catch (error: any) {
    logger.error(`Error verifying 2FA token: ${error.message}`);
    handleHttpException(error, res);
  }
}
