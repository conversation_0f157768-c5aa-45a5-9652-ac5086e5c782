import { Request, Response } from 'express';
import DashboardUser from '../models/mongo/userSchema';
import bcrypt from 'bcrypt';
import { getUserById, getUsers, createUser, updateUser, resetPassword } from '../services/userService';
import { UserQuery, UserBody } from '../interfaces/mongoUser';
import logger from '../../submodules/ryvyl-commons/services/loggerService';
import { jsonParse } from '../../submodules/ryvyl-commons/utils/jsonParse';
import get2FAAuthToken from '../helpers/get2FAAuthToken';

export async function verifyUser(request: Request, response: Response): Promise<void> {
  const { username, password } = request.body;
  try {
    const user = await DashboardUser.findOne({ username: username }).exec();
    if (!user) {
      response.status(401).json({ message: 'Invalid Credentials' });
      return;
    } else if (user.isBlocked) {
      response.status(401).json({ message: 'User is blocked' });
      return;
    } else if (!(await bcrypt.compare(password, user.password))) {
      response.status(401).json({ message: 'Invalid Credentials' });
      return;
    } else {
      // Generate a temporary token for 2FA verification
      const twoFAToken = get2FAAuthToken(user);

      // Check if 2FA is enabled for this user
      if (user.twoFactorSecret) {
        // Return the temporary token along with 2FA flag
        response.status(200).json({
          twoFactorEnabled: true,
          twoFAToken: twoFAToken
        });
        return;
      }

      // If 2FA is not enabled, proceed with setting it up
      response.status(200).json({ twoFAToken: twoFAToken });
    }
  } catch (error: any) {
    logger.error(`Error verifying user: ${error.message}`);
    response.status(500).json({ message: 'Something went wrong!' });
  }
}

/**
 * Create a new user
 */
export async function createUserController(req: Request, res: Response): Promise<void> {
  try {
    const { username, role, name } = req.body;

    const body: UserBody = {
      username,
      role,
      name,
      isBlocked: false
    };

    // Create new user
    const user = await createUser(body);
    res.status(201).json({ data: user });
  } catch (error: any) {
    logger.error(`Error creating user: ${error.message}`);
    res.status(400).json({ message: jsonParse(error.message) });
  }
}

/**
 * Get all users with filtering, sorting, and pagination
 */
export async function getUsersController(req: Request, res: Response): Promise<void> {
  try {
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const sortBy = req.query.sortBy as string;
    const sortOrder = req.query.sortOrder as string;
    const username = req.query.username as string;
    const role = req.query.role as string;
    const name = req.query.name as string;

    // Convert isBlocked to boolean if it exists
    let isBlocked: boolean | undefined = undefined;
    if (req.query.isBlocked !== undefined) {
      isBlocked = req.query.isBlocked === 'true';
    }

    // Filter object
    const queryParams: UserQuery = {
      username,
      role,
      name,
      isBlocked
    };

    const result = await getUsers(queryParams, page, limit, sortBy, sortOrder);
    res.status(200).json(result);
  } catch (error: any) {
    logger.error(`Error fetching users: ${error.message}`);
    res.status(400).json({ message: error.message });
  }
}

/**
 * Get user by ID
 */
export async function getUserByIdController(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const user = await getUserById(id);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    res.status(200).json({ data: user });
  } catch (error: any) {
    logger.error(`Error fetching user: ${error.message}`);
    res.status(400).json({ message: error.message });
  }
}

/**
 * Update user
 */
export async function updateUserController(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { userId } = req.user as {
      username: string;
      userId: string;
      role: string;
      roleIsActive: boolean;
      permissions: {
        action: string;
        object: string;
        description?: string;
        isActive: boolean;
      }[];
    };
    const { username, role, name, password, isBlocked, oldPassword } = req.body;

    if (username) {
      // Check if username already exists for a different user
      const existingUser = await DashboardUser.findOne({
        username,
        _id: { $ne: id } // exclude current user from check
      }).exec();

      if (existingUser) {
        res.status(400).json({ message: 'Username already exists' });
        return;
      }
    }

    const updatedUser = await updateUser(id || userId, {
      username,
      role,
      name,
      password,
      isBlocked,
      oldPassword
    });
    res.status(200).json({ data: updatedUser });
  } catch (error: any) {
    logger.error(`Error updating user: ${error.message}`);
    res.status(400).json({ message: jsonParse(error.message) });
  }
}

/**
 * Reset user password
 */
export async function resetPasswordController(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;

    // Get the user to ensure they exist
    const user = await getUserById(id);
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Reset the password
    await resetPassword(id);

    res.status(200).json({ message: 'Password reset email has been sent to the user' });
  } catch (error: any) {
    logger.error(`Error resetting password: ${error.message}`);
    res.status(400).json({ message: error.message });
  }
}
