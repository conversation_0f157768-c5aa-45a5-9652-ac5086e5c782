import {
  getTransactionsForAMLCheck,
  checkIfTransactionExistForAState,
  getForVisaTransactions,
  setLastCheckedAMLRecord,
  lastCheckedAMLRecord,
  setLastCheckedVisaDirectionRecord,
  lastCheckedVisaDirectionRecord,
  getForVisaAPITransactions
} from './oracleService';
import { insertResultsDB } from '../services/oracleService';
import {
  createPayload,
  extractVisaErrorDetailsMessage,
  getAccountBalances,
  sendToVisaTransaction,
  validatePayout
} from './visaService';
import cron from 'node-cron';
import {
  AMLTransactionExistInMongo,
  visaCheckReferenceIdExist,
  insertFilteredTransactions,
  insertVisaTransactions,
  initiateVisaTxStartingPoint,
  insertVisaBalance,
  insertInvalidTransaction,
  initiateAmlTxStartingPoint,
  AMLFindTransactionByRefNumber
} from './mongoService';
import { amlLogger, jotFormLogger, visaLogger } from '../utils/logger';
import { Transaction } from '../interfaces/oracleTransactionInterface';
import { sleep } from '../utils/sleep';
import { checkTransactionInSumsub } from './sumsubService';
import {
  findPaxumVisaTransactionByUniqueId,
  updatePaxumVisaTransactionMessage,
  updatePaxumVisaTransactionToProceed
} from './paxumVisaTransaction';
import { PaxumVisaTransactionInterface } from '../interfaces/paxumVisaTransactionInterface';
import { getUnsuccessfulWebhooks } from './webhookService';
import { TYPE } from '../enums/webhookEnums';
import { sendUserWebhookInformation } from './jotformService';
import { WebhookApplicant } from '../interfaces/sumsubApplicant';
import { AmlData } from '../interfaces/AmlData';

import logger from '../../submodules/ryvyl-commons/services/loggerService';

let uniqueIdsGettingChecked: Set<number> = new Set();

let amlCronStarted: boolean = false;
let amlCronActive: boolean = false;
let vdCronStarted: boolean = false;
let vdCronActive: boolean = false;
let vdBalancesCronStarted: boolean = false;
let vdBalancesCronActive: boolean = false;
let resendJotformWebhookCronStarted: boolean = false;
let resendJotformWebhookCronActive: boolean = false;

export async function startAMLCronJob() {
  if (!amlCronStarted) {
    amlCronStarted = true;
    amlLogger.info('Cron initiated.');
    cron.schedule('* * * * *', async () => {
      if (amlCronActive) {
        amlLogger.info('Cron is active. Skipping cycle.');
        return;
      }
      amlCronActive = true;
      await executeAMLCheck();
      amlCronActive = false;
    });
  }
}

export async function initVisaDirectCron() {
  if (!vdCronStarted) {
    vdCronStarted = true;
    visaLogger.info('Cron initiated.');

    cron.schedule('* * * * *', async () => {
      if (vdCronActive) {
        visaLogger.info('Cron is active. Skipping cycle.');
        return;
      }
      vdCronActive = true;

      await processVisaTransfers();

      // This is when we connect with paxum
      await processVisaTransfersWithPaxumData();
      vdCronActive = false;
    });
  }
}

export async function initVisaDirectBalancesCron() {
  if (!vdBalancesCronStarted) {
    vdBalancesCronStarted = true;
    visaLogger.info('Cron initiated.');

    cron.schedule('*/30 * * * * *', async () => {
      if (vdBalancesCronActive) {
        visaLogger.info('Cron is active. Skipping cycle.');
        return;
      }

      vdBalancesCronActive = true;
      await fetchVisaDirectBalances();
      vdBalancesCronActive = false;
    });
  }
}

export async function processResendJotformWebhookCron() {
  if (!resendJotformWebhookCronStarted) {
    resendJotformWebhookCronStarted = true;
    jotFormLogger.info('Cron initiated.');

    // Cron will run in every 15 and 45 seconds from the minute
    cron.schedule('15,45 * * * * *', async () => {
      if (resendJotformWebhookCronActive) {
        jotFormLogger.info('Cron is active. Skipping cycle.');
        return;
      }
      resendJotformWebhookCronActive = true;
      await handleUnsuccessfulJotFormWebhooks();
      resendJotformWebhookCronActive = false;
    });
  }
}

async function executeAMLCheck() {
  await initiateAmlTxStartingPoint();

  // Step 1: Getting transactions from Oracle DB
  try {
    const transactions = await getTransactionsForAMLCheck();

    // Step 2: Send the filtered transactions to AML service for analyzing
    if (transactions?.length > 0) {
      amlLogger.info(
        `AML cron cycle started with ${transactions.length} transactions at ${new Date().toLocaleString()}`
      );

      for (let i = 0; i < transactions.length; i += 50) {
        const batch = transactions.slice(i, i + 50);

        await Promise.all(
          batch.map(async (record) => {
            if (uniqueIdsGettingChecked.has(record.UNIQUE_ID)) {
              logger.info(
                `AML check already running for unique id ${record.UNIQUE_ID} and reference ${record.REFERENCE_NUMBER}`
              );
              return;
            }

            uniqueIdsGettingChecked.add(record.UNIQUE_ID);
            amlLogger.info(
              `Starting AML check for unique id ${record.UNIQUE_ID} and reference ${record.REFERENCE_NUMBER}`
            );

            const transactionCheck = await handleTransactionCheck(record);

            if (transactionCheck == 'continue') {
              setLastCheckedAMLRecord(record.UNIQUE_ID);
              uniqueIdsGettingChecked.delete(record.UNIQUE_ID);
              return;
            } else if (transactionCheck == 'terminate') {
              return;
            }

            try {
              const amlResult = await checkTransactionInSumsub(record);

              if (amlResult) {
                const mongoDbData = {
                  transactionType: record.TRANSACTION_TYPE,
                  amount: record.AMOUNT_ORIGINAL_CURRENCY,
                  currencyCode: record.ORIGINAL_CURRENCY,
                  ...amlResult
                };
                // Step 6: Saving the results in MongoDB (ILDB - transactions)
                const mongoDBResult = await insertFilteredTransactions(mongoDbData);
                if (!mongoDBResult) {
                  amlLogger.error('FAILED_INSERT_DATA_MONGODB_ROLLING_BACK_ORACLEDB_CHANGES');
                  return false;
                }

                // Step 7: Saving the results in Oracle DB (AML_RESULT_TRANSACTIONS table)
                const oracleResult = await insertResultsDB(amlResult, 1);
                if (!oracleResult) {
                  amlLogger.error('FAILED_INSERT_ORACLEDB');
                  return;
                }

                amlLogger.info(`Successful check for transaction with reference ${amlResult.referenceNumber}`);
                uniqueIdsGettingChecked.delete(record.UNIQUE_ID);
              }
            } catch (error) {
              logger.error(`Unable to initiate AML Check - ${error}`);
            }
          })
        );

        if (uniqueIdsGettingChecked.size > 0) {
          const minUniqueId = Math.min(...Array.from(uniqueIdsGettingChecked));
          setLastCheckedAMLRecord(minUniqueId - 1, true);
          logger.info(`Going back to last unique id: ${lastCheckedAMLRecord}`);
          uniqueIdsGettingChecked.clear();
          break;
        } else {
          const maxUniqueId = Math.max(...batch.map((record) => record.UNIQUE_ID));
          setLastCheckedAMLRecord(maxUniqueId);
        }

        logger.info(`Finished check with last unique id: ${lastCheckedAMLRecord}`);
        await sleep(1000);
      }
      amlLogger.info(`AML cron cycle finished at ${new Date().toLocaleString()}`);
    } else {
      amlLogger.info(`AML cron cycle finished with no new transactions`);
    }
  } catch (error: any) {
    amlLogger.error(`AML error: ${error.message}`);
  }
}

export async function processVisaTransfers() {
  // Step 0: Set starting point from which Visa Direct transactions should be processed
  await initiateVisaTxStartingPoint();

  // Step 1: Getting transactions from Oracle DB (AML_CHECK_TRANSACTIONS table), status 8
  const transactions = await getForVisaTransactions();

  // Step 2: Use Set() to collect their REFERENCE_ID
  const referenceIdSet = new Set<string>();
  for (const record of transactions) {
    referenceIdSet.add(record.REFERENCE_ID);
  }

  // Step 3: Check in our MongoDB table `transactions` if those records exists and return array with the duplicates
  const duplicates = await visaCheckReferenceIdExist(referenceIdSet);

  // Step 4: Filter transactions to exclude those with a REFERENCE_ID found in duplicates
  const filteredTransactions = transactions.filter(
    (transaction: Transaction) => !duplicates.has(transaction.REFERENCE_ID)
  );

  if (filteredTransactions.length > 0) {
    visaLogger.info(
      `Visa cron cycle started with ${filteredTransactions.length} transactions at ${new Date().toLocaleString()}`
    );
    for (const record of filteredTransactions) {
      // Step 5: Validate the payout before sending it
      try {
        const payload = await createPayload({ data: record, from: 'orasys' });

        if (payload == undefined) {
          continue;
        }

        //DEBUG
        //Payload prepared for VISA
        visaLogger.info('Payload sent to VISA');
        visaLogger.info(JSON.stringify(payload, null, 4));

        const referenceId = payload.transactionDetail.clientReferenceId ?? '';
        let validatedPayout = await validatePayout(payload);

        if (validatedPayout) {
          if (validatedPayout.validationResultCode === 'INVALID' || validatedPayout.validationResultCode === 'ERROR') {
            visaLogger.debug(`Visa transaction with reference id ${record.REFERENCE_ID} has invalid payout`);
            // Step 6a: If INVALID save the result in IL database for later call to action in the dashboard
            let message = '';
            if (validatedPayout.validationResultCode === 'INVALID') {
              message = validatedPayout
                .validationDetails!![0].details.map((detail) => `${detail.message} - ${detail.location}`)
                .join('\r\n');
            } else {
              message = JSON.stringify(validatedPayout.validationDetails!![0]);
            }

            payload.transactionDetail.clientReferenceId = referenceId;
            const mongoDBResult = await insertInvalidTransaction(referenceId, message, payload, record.UNIQUE_ID);
            if (!mongoDBResult) {
              visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
              return false;
            }
          } else {
            // Step 6b: Send the payout to Visa
            payload.transactionDetail.clientReferenceId = record.REFERENCE_ID;

            let visaResult = await sendToVisaTransaction(payload);
            const decryptedStatus = visaResult?.data.transactionDetail?.status;
            let status;
            switch (visaResult.statusCode) {
              case 202:
                status = 'P';
                break;

              case 200:
                status = decryptedStatus === 'PAYMENT_RECEIVED' ? 'T' : 'P';
                break;

              default:
                status = 'F';
                break;
            }
            const data = {
              uniqueId: record.UNIQUE_ID,
              mode_check: 2,
              status: visaResult.statusCode === 200 && decryptedStatus === 'PAYMENT_RECEIVED' ? 'T' : 'F',
              visaStatus: decryptedStatus.replace('_', ' '),
              visaMsg: visaResult.statusCode === 200 ? '' : 'UNKNOWN_REASON',
              referenceId: visaResult?.data.transactionDetail?.clientReferenceId ?? record.REFERENCE_ID,
              referenceNumber: record.REFERENCE_NUMBER
            };
            if (visaResult) {
              // Step 7: Saving the results in Oracle DB (AML_RESULT_TRANSACTIONS table)
              const oracleResult = await insertResultsDB(data, 2);
              if (!oracleResult) {
                visaLogger.debug('FAILED_INSERT_ORACLEDB');
                return false;
              }

              // Step 8: Saving the results in MongoDB (ILDB - visa transactions)
              const mongoDBResult = await insertVisaTransactions(data);
              if (!mongoDBResult) {
                visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
                return false;
              }
              visaLogger.debug(`Transaction with reference id ${record.REFERENCE_ID} successfully sent to Visa`);
            }
          }
        }
      } catch (error: any) {
        visaLogger.error(
          `Process Visa transaction error: Transaction with reference id ${record.REFERENCE_ID} has error ${error.message}`
        );
      }

      setLastCheckedVisaDirectionRecord(record.UNIQUE_ID);
      await sleep(150);
    }
    visaLogger.info(`Finished Visa direction check with last unique id: ${lastCheckedVisaDirectionRecord}`);
    visaLogger.info(`Visa cron cycle finished at ${new Date().toLocaleString()}`);
  } else {
    visaLogger.info(`Visa cron cycle finished with no new transactions`);
  }
}

export async function fetchVisaDirectBalances() {
  try {
    const balances = await getAccountBalances();

    if (balances && balances.length > 0) {
      visaLogger.info(`Visa balances cron job started at ${new Date().toLocaleString()}`);
      for (const balance of balances) {
        await insertVisaBalance(balance);
      }
      visaLogger.info(`Visa balances cron job finished with updated balances at ${new Date().toLocaleString()}`);
    }
  } catch (error: any) {
    let message = '';
    if (error.errorResponse) {
      message = extractVisaErrorDetailsMessage(error.errorResponse);
    } else {
      message = error.message;
    }

    visaLogger.error(`Visa balances error: ${JSON.stringify(message)}`);
  }
}

async function processVisaTransfersWithPaxumData() {
  // Step 0: Set starting point from which Visa Direct transactions should be processed
  await initiateVisaTxStartingPoint();

  // Step 1: Getting transactions from Oracle DB (AML_CHECK_TRANSACTIONS table), status 8
  const transactions = await getForVisaAPITransactions();

  // Step 2: Use Set() to collect their REFERENCE_ID
  const referenceIdSet = new Set<string>();
  for (const record of transactions) {
    referenceIdSet.add(record.REFERENCE_ID);
  }

  // Step 3: Check in our MongoDB table `transactions` if those records exists and return array with the duplicates
  const duplicates = await visaCheckReferenceIdExist(referenceIdSet);

  // Step 4: Filter transactions to exclude those with a REFERENCE_ID found in duplicates
  const filteredTransactions = transactions.filter(
    (transaction: Transaction) => !duplicates.has(transaction.REFERENCE_ID)
  );

  if (filteredTransactions.length > 0) {
    visaLogger.info(
      `Visa cron cycle for PAXUM started with ${filteredTransactions.length} transactions at ${new Date().toLocaleString()}`
    );
    for (const record of filteredTransactions) {
      // Step 5: Validate the payout before sending it
      try {
        const paxumUniqueId = record?.REASON?.split(',')[0];

        if (!paxumUniqueId) {
          throw new Error('UniqueId is not provided!');
        }
        const paxumTransaction = await findPaxumVisaTransactionByUniqueId(paxumUniqueId);

        if (!paxumTransaction) {
          throw new Error(`Paxum does not provide transaction with paxumUniqueId: ${paxumUniqueId}`);
        } else if (paxumTransaction.isProceed) {
          throw new Error(`Transaction with paxumUniqueId: ${paxumUniqueId}, has been proceed!`);
        }

        const compareTransaction = compareTwoTransactions(record, paxumTransaction);

        if (compareTransaction) {
          await updatePaxumVisaTransactionMessage(paxumUniqueId, compareTransaction);
          const data = {
            uniqueId: record.UNIQUE_ID,
            status: 'F',
            referenceId: record.REFERENCE_ID
          };
          const oracleResult = await insertResultsDB(data, 2);
          if (!oracleResult) {
            visaLogger.debug('processVisaTransfersWithPaxumData: Unmatched transaction data: FAILED_INSERT_ORACLEDB');
          }
          throw new Error(`Transactions with paxumUniqueId: ${paxumUniqueId} values are not the same!`);
        }

        const payload = await createPayload({
          data: record,
          from: 'orasys-paxum',
          paxumTransaction: paxumTransaction ?? undefined
        });

        if (payload == undefined) {
          continue;
        }

        const referenceId = payload.transactionDetail.clientReferenceId ?? '';
        let validatedPayout = await validatePayout(payload);

        if (validatedPayout) {
          if (validatedPayout.validationResultCode === 'INVALID' || validatedPayout.validationResultCode === 'ERROR') {
            visaLogger.debug(`Visa transaction with reference id ${record.REFERENCE_ID} has invalid payout`);
            // Step 6a: If INVALID save the result in IL database for later call to action in the dashboard
            let message = '';
            if (validatedPayout.validationResultCode === 'INVALID') {
              message = validatedPayout
                .validationDetails!![0].details.map((detail) => `${detail.message} - ${detail.location}`)
                .join('\r\n');
            } else {
              message = JSON.stringify(validatedPayout.validationDetails!![0]);
            }
            payload.transactionDetail.clientReferenceId = referenceId;
            const mongoDBResult = await insertInvalidTransaction(referenceId, message, payload, record.UNIQUE_ID);
            if (!mongoDBResult) {
              visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
              return false;
            }
          } else {
            // Step 6b: Send the payout to Visa
            payload.transactionDetail.clientReferenceId = record.REFERENCE_ID;
            let visaResult = await sendToVisaTransaction(payload);
            const decryptedStatus = visaResult?.data.transactionDetail?.status;
            let status;
            switch (visaResult.statusCode) {
              case 202:
                status = 'P';
                break;
              case 200:
                status = decryptedStatus === 'PAYMENT_RECEIVED' ? 'T' : 'P';
                break;
              default:
                status = 'F';
                break;
            }
            const data = {
              uniqueId: record.UNIQUE_ID,
              mode_check: 2,
              status: visaResult.statusCode === 200 && decryptedStatus === 'PAYMENT_RECEIVED' ? 'T' : 'F',
              visaStatus: decryptedStatus.replace('_', ' '),
              visaMsg: visaResult.statusCode === 200 ? '' : 'UNKNOWN_REASON',
              referenceId: visaResult?.data.transactionDetail?.clientReferenceId ?? record.REFERENCE_ID,
              referenceNumber: record.REFERENCE_NUMBER
            };
            if (visaResult) {
              // Step 7: Saving the results in Oracle DB (AML_RESULT_TRANSACTIONS table)
              const oracleResult = await insertResultsDB(data, 2);
              if (!oracleResult) {
                visaLogger.debug('FAILED_INSERT_ORACLEDB');
                return false;
              }
              // Step 8: Saving the results in MongoDB (ILDB - visa transactions)
              const mongoDBResult = await insertVisaTransactions(data);
              if (!mongoDBResult) {
                visaLogger.debug('FAILED_INSERT_DATA_MONGODB');
                return false;
              }

              visaLogger.debug(`Transaction with reference id ${record.REFERENCE_ID} successfully sent to Visa`);
            }
          }
          const isPaxumTransactionUpdated = updatePaxumVisaTransactionToProceed(paxumUniqueId);
          if (!isPaxumTransactionUpdated) {
            visaLogger.debug('FAILED_UPDATE_PAXUM_VISA_TRANSACTION');
            return false;
          }
        }
      } catch (error: any) {
        visaLogger.error(
          `Process Visa transaction error: Transaction with reference id ${record.REFERENCE_ID} has error: ${error.message}`
        );
      }

      setLastCheckedVisaDirectionRecord(record.UNIQUE_ID);
      await sleep(150);
    }
    visaLogger.info(`Finished Visa direction check with last unique id: ${lastCheckedVisaDirectionRecord}`);
    visaLogger.info(`Visa cron cycle for PAXUM finished at ${new Date().toLocaleString()}`);
  } else {
    visaLogger.info(`Visa cron cycle for PAXUM finished with no new transactions`);
  }
}

function compareTwoTransactions(
  orasysTransaction: Transaction,
  paxumTransaction: PaxumVisaTransactionInterface
): string | undefined {
  let message = '';

  if (
    orasysTransaction.BENEFICIARY_ACCOUNT_NUMBER.trim() !==
    paxumTransaction?.additionalFields?.get('recipientAccountNumber')
  ) {
    const currentMessage = 'Account numbers have some differences in both transactions!';
    message += message ? `\r\n ${currentMessage}` : currentMessage;
  }

  if (orasysTransaction.AMOUNT_ORIGINAL_CURRENCY !== Number(paxumTransaction?.amount)) {
    const currentMessage = 'Amount has some differences in both transactions!';
    message += message ? `\r\n ${currentMessage}` : currentMessage;
  }

  if (orasysTransaction.ORIGINAL_CURRENCY !== paxumTransaction?.currencyCode) {
    const currentMessage = 'CurrencyCode has some differences in both transactions!';
    message += message ? `\r\n ${currentMessage}` : currentMessage;
  }

  let recipientNames = paxumTransaction?.additionalFields?.get('recipientNames');
  let recipientFirstName = paxumTransaction?.additionalFields?.get('recipientFirstName');
  let recipientLastName = paxumTransaction?.additionalFields?.get('recipientLastName');
  // Check for recipientNames
  if (recipientNames) {
    recipientNames = recipientNames.toUpperCase();
    if (!orasysTransaction.BENEFICIARY_NAME_AND_ADDRESS.includes(recipientNames)) {
      const currentMessage = 'Recipient Names have some differences in both transactions!';
      message += message ? `\r\n${currentMessage}` : currentMessage;
    }
  }
  // Check for recipientFirstName and recipientLastName
  if (recipientFirstName && recipientLastName) {
    recipientFirstName = recipientFirstName.toUpperCase();
    recipientLastName = recipientLastName.toUpperCase();
    if (
      !orasysTransaction.BENEFICIARY_NAME_AND_ADDRESS.includes(recipientFirstName) ||
      !orasysTransaction.BENEFICIARY_NAME_AND_ADDRESS.includes(recipientLastName)
    ) {
      const currentMessage = 'Recipient Names have some differences in both transactions!';
      message += message ? `\r\n${currentMessage}` : currentMessage;
    }
  }

  // If recipientNames and one of the first or last names is missing
  if (!recipientNames && (!recipientFirstName || !recipientLastName)) {
    const currentMessage = 'First Name or Last Name is not provided!';
    message += message ? `\r\n${currentMessage}` : currentMessage;
  }

  return message || undefined;
}

async function handleTransactionCheck(record: Transaction): Promise<'continue' | 'terminate' | 'run the check'> {
  const checkForExistingInOracle =
    record.TRANSACTION_TYPE !== 'IS' && (await checkIfTransactionExistForAState(record.REFERENCE_ID));
  const checkForExistingInMongoDb = await AMLTransactionExistInMongo(record.REFERENCE_ID, record.REFERENCE_NUMBER);

  if (checkForExistingInOracle && checkForExistingInMongoDb) {
    amlLogger.info(
      `A transaction with reference number: ${record.REFERENCE_NUMBER} exists in Oracle and Mongo databases`
    );
    return 'continue';
  } else if (checkForExistingInOracle) {
    amlLogger.info(`A transaction with reference number: ${record.REFERENCE_NUMBER} exists in Oracle with Status > 3`);
    return 'continue';
  } else if (checkForExistingInMongoDb) {
    const transactionByRefNumber = await AMLFindTransactionByRefNumber(record.REFERENCE_NUMBER);
    amlLogger.info(`A transaction with reference number: ${record.REFERENCE_NUMBER} existing in Mongo`);

    if (!transactionByRefNumber) {
      amlLogger.error(`Cannot find transaction with reference number: ${record.REFERENCE_NUMBER} in Mongo`);
      return 'continue';
    }

    if (transactionByRefNumber?.referenceId === record.REFERENCE_ID) {
      const dataOracle: AmlData = {
        uniqueId: record.UNIQUE_ID,
        status: transactionByRefNumber.status,
        referenceId: record.REFERENCE_ID,
        referenceNumber: record.REFERENCE_NUMBER
      };

      // Saving the results in Oracle DB (AML_RESULT_TRANSACTIONS table)
      const oracleResult = await insertResultsDB(dataOracle, 1);

      if (!oracleResult) {
        amlLogger.error('FAILED_INSERT_ORACLEDB');
        return 'terminate';
      }
    }
    return 'continue';
  }

  return 'run the check';
}

async function handleUnsuccessfulJotFormWebhooks() {
  try {
    const unsuccessWebhooks = await getUnsuccessfulWebhooks(TYPE.JOTFORM_APPLICANT);
    const CONCURRENT_LIMIT = 20;

    // Process webhooks in chunks
    if (unsuccessWebhooks.length > 0) {
      jotFormLogger.info(`Resend Jotform webhook cron cycle started at ${new Date().toLocaleString()}`);
      for (let i = 0; i < unsuccessWebhooks.length; i += CONCURRENT_LIMIT) {
        const chunk = unsuccessWebhooks.slice(i, i + CONCURRENT_LIMIT);

        const promises = chunk
          .filter((webhook) => webhook.url)
          .map((webhook) => sendUserWebhookInformation(webhook.dataToSend as WebhookApplicant, webhook.url!, webhook));

        await Promise.all(promises);
      }
      jotFormLogger.info(`Resend Jotform webhook cron cycle finished at ${new Date().toLocaleString()}`);
    } else {
      jotFormLogger.info('No unsuccessful webhooks to resend');
    }
  } catch (error: any) {
    jotFormLogger.error(`Error processing Jotform webhook: ${error.message}`);
  }
}

export async function startManualAMLCheck(fromUniqueId: number): Promise<{ success: boolean; message: string }> {
  if (amlCronActive) {
    return {
      success: false,
      message: 'AML cron job is currently active. Please try again later.'
    };
  }

  amlCronActive = true;
  setLastCheckedAMLRecord(fromUniqueId - 1, true);
  amlCronActive = false;
  return {
    success: true,
    message: 'Last checked AML record successfully updated'
  };
}
