import bcrypt from 'bcrypt';
import DashboardUser from '../models/mongo/userSchema';
import { Role } from '../interfaces/mongoRole';
import { UserBody, UserQuery } from '../interfaces/mongoUser';
import { getRoleByName } from './roleService';
import { hashPassword } from '../utils/hashPassword';
import { kafkaService } from '../../submodules/ryvyl-commons/services/kafkaService';
import { generateSecret } from './twoFactorService';
import { setRyvylAdminIsBlocked } from '../../submodules/ryvyl-commons/services/redisService';
import { ObjectId } from 'mongoose';

export async function getUserRole(userName: string): Promise<Role | undefined> {
  try {
    const user = await DashboardUser.findOne({ username: userName })
      .populate({
        path: 'role',
        populate: {
          path: 'permissions'
          // select: { _id: 0, object: 1, action: 1 }
        }
      })
      .exec();

    if (user && user.role && user.role?.permissions?.length > 0) {
      return user.role;
    } else {
      return undefined;
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
}

export async function getUserById(id: string) {
  const user = await DashboardUser.findById(id).populate('role');
  return user;
}

export async function getUsers(
  queryParams: UserQuery,
  page?: number,
  limit?: number,
  sortBy?: string,
  sortOrder?: string
) {
  const { username, role, name, isBlocked } = queryParams;
  let filter: any = {};

  // Add filters
  if (username) {
    filter.username = { $regex: new RegExp(username, 'i') };
  }

  if (name) {
    filter.name = { $regex: new RegExp(name, 'i') };
  }

  if (typeof isBlocked === 'boolean') {
    filter.isBlocked = isBlocked;
  }

  if (role) {
    const roleObj = await getRoleByName(role);
    if (roleObj) {
      filter.role = roleObj._id;
    }
  }

  let validSortFields = Object.keys(DashboardUser.schema.obj);
  validSortFields = [...validSortFields, 'createdAt', 'updatedAt'];

  const defaultSortField = 'createdAt';
  const defaultSortOrder = -1;

  const sortField = sortBy && validSortFields.includes(sortBy) ? sortBy : defaultSortField;
  const sortDirection = sortOrder === 'asc' ? 1 : defaultSortOrder;

  // Create sort object
  let sortOptions = {};

  // Special case for isBlocked: sort by isBlocked first, then by updatedAt
  if (sortField === 'isBlocked') {
    sortOptions = {
      [sortField]: sortDirection,
      createdAt: -1
    };
  } else {
    sortOptions = { [sortField]: sortDirection };
  }

  const options = {
    page: page || 1,
    limit: limit || 10,
    sort: sortOptions,
    populate: 'role',
    lean: false,
    // Add collation for case-insensitive sorting
    collation: {
      locale: 'en',
      strength: 2 // Case-insensitive comparison
    }
  };

  const result = await DashboardUser.paginate(filter, options);
  return result;
}

export async function getUserByUsername(username: string) {
  const user = await DashboardUser.findOne({ username });
  return user;
}

export async function createUser(userData: UserBody) {
  // Check if user already exists
  const existingUser = await getUserByUsername(userData.username);
  if (existingUser) {
    throw new Error('User with this username already exists');
  }

  // Validate role if provided
  if (userData.role) {
    // Check if role exists
    const role = await getRoleByName(userData.role);
    if (!role) {
      throw new Error('Role does not exist');
    }
  }

  const password = Math.random().toString(36).slice(-8); // 8 characters random string
  // hash the password
  const { hashedPassword } = await hashPassword(password);

  const newUser = await DashboardUser.create({
    ...userData,
    password: hashedPassword,
    role: (await getRoleByName(userData.role))?._id
  });

  newUser._id &&
    kafkaService.publish('user-created', newUser._id.toString(), {
      email: userData.username,
      password: password,
      name: userData.name
    });

  return newUser;
}

export async function updateUser(id: string, userData: Partial<UserBody>) {
  // Check if user exists
  const userToUpdate = await getUserById(id);
  if (!userToUpdate) {
    throw new Error('User not found');
  }

  if (userData.username && userData.username !== userToUpdate.username) {
    // Check if username already exists
    const existingUser = await getUserByUsername(userData.username);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }
  }

  // Validate role if provided
  if (userData.role) {
    // Check if role exists
    const role = await getRoleByName(userData.role);

    if (!role) {
      throw new Error('Role does not exist');
    }
    userToUpdate.role = role._id as any; // Type cast for mongoose ObjectId
  }

  if (userData.password && userData.oldPassword) {
    if (!(await bcrypt.compare(userData.oldPassword, userToUpdate.password))) {
      throw new Error('Invalid current password');
    }
    const { hashedPassword } = await hashPassword(userData.password);
    userToUpdate.password = hashedPassword;
  }

  // Update user properties
  if (userData.username) userToUpdate.username = userData.username;
  if (userData.name) userToUpdate.name = userData.name;
  if (userData.isBlocked !== undefined) {
    userToUpdate.isBlocked = userData.isBlocked;
  }

  const updatedUser = await userToUpdate.save();

  if (userData.isBlocked !== undefined) {
    setRyvylAdminIsBlocked((userToUpdate._id as ObjectId).toString(), userData.isBlocked);
  }

  return updatedUser;
}

/**
 * Reset user password and send email with Kafka
 * @param id User ID
 */
export async function resetPassword(id: string) {
  // Check if user exists
  const user = await getUserById(id);
  if (!user) {
    throw new Error('User not found');
  }

  // Generate a random password
  const password = Math.random().toString(36).slice(-8);

  // Hash the password
  const { hashedPassword } = await hashPassword(password);

  // Update the user's password
  user.password = hashedPassword;
  user.twoFactorSecret = undefined; // Reset 2FA when password is reset
  await user.save();
  await kafkaService.publish('password-reset', id, {
    email: user.username,
    name: user.name,
    password: password
  });

  return true;
}
