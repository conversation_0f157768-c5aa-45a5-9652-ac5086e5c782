import HmacSHA256 from 'crypto-js/hmac-sha256';
import encHex from 'crypto-js/enc-hex';
import crypto from 'crypto';

import { Transaction } from '../interfaces/oracleTransactionInterface';
import { amlLogger } from '../utils/logger';
import { AmlData } from '../interfaces/AmlData';
import { SumsubRequestData } from '../types/sumsub';
import { sleep } from '../utils/sleep';
import { classifyCustomerType, TypeOfCustomer } from './aiCustomerClassifier';
import countries from 'i18n-iso-countries';
import logger from '../../submodules/ryvyl-commons/services/loggerService';

export async function checkTransactionInSumsub(record: Transaction, retryCount = 0): Promise<AmlData> {
  const direction =
    record.TRANSACTION_TYPE == 'IT' || record.TRANSACTION_TYPE == 'IS' || record.TRANSACTION_TYPE == 'II'
      ? 'in'
      : 'out';
  let transferDataForIndividual;
  let transferDataForCompany;

  if (direction === 'out') {
    transferDataForIndividual = formDataToSend(record, 'out', 'company', 'individual');
    transferDataForCompany = formDataToSend(record, 'out', 'company', 'company');
  } else {
    transferDataForIndividual = formDataToSend(record, 'in', 'individual', 'company');
    transferDataForCompany = formDataToSend(record, 'in', 'company', 'company');
  }

  try {
    let firstAmlData;
    let secondAmlData;
    // Classify the counterparty only, the applicant is already registered in Ryvyl and no need to check for AML
    let nameToClassify = transferDataForIndividual.counterparty.fullName;
    const aiClassifierResult = await classifyCustomerType(nameToClassify);

    if (aiClassifierResult === TypeOfCustomer.Person || aiClassifierResult === TypeOfCustomer.Unknown) {
      firstAmlData = await fetchDataFromSumsub(
        transferDataForIndividual,
        record.UNIQUE_ID,
        record.REFERENCE_ID,
        record.REFERENCE_NUMBER
      );
    }

    if (aiClassifierResult === TypeOfCustomer.Company || aiClassifierResult === TypeOfCustomer.Unknown) {
      secondAmlData = await fetchDataFromSumsub(
        transferDataForCompany,
        record.UNIQUE_ID,
        record.REFERENCE_ID,
        record.REFERENCE_NUMBER
      );
    }

    if (firstAmlData?.status === 'F') return Promise.resolve(firstAmlData);
    else if (secondAmlData?.status === 'F') return Promise.resolve(secondAmlData);
    else return Promise.resolve(firstAmlData ?? secondAmlData!);
  } catch {
    if (retryCount < 5) {
      logger.info(`Retrying Sumsub request (attempt ${retryCount + 1})...`);
      await sleep(50);
      return checkTransactionInSumsub(record, retryCount + 1);
    } else {
      logger.error('Max retries reached for Sumsub request.');
      throw new Error('Max retries reached for Sumsub request.');
    }
  }
}

export const fetchDataFromSumsub = async function (
  data: SumsubRequestData,
  uniqueId: number,
  referenceId: string,
  referenceNumber: string
): Promise<AmlData> {
  try {
    let response = (await sumsubCheckTransactionRequest(data)) as any;

    if (response.status == 409) {
      amlLogger.error(`HTTP_ERROR_STATUS ${response.status} for reference number ${referenceNumber}`);
      response = await getTransactionsInformation(data.txnId);
    }

    if (!response.ok) {
      const errorBody = await response.json();
      amlLogger.error(`HTTP_ERROR_STATUS ${response.status}`);
      amlLogger.error(`Message sumsub request error: ${errorBody.description}`);
      throw new Error(`HTTP_ERROR_STATUS ${response.status} for reference number: ${referenceNumber} `);
    }

    const jsonData = (await response.json()) as any;

    let statusTransaction = jsonData.review?.reviewResult?.reviewAnswer;

    const sumsubData: AmlData = {
      uniqueId: uniqueId,
      status: statusTransaction == 'GREEN' ? 'T' : 'F',
      referenceId: referenceId,
      referenceNumber: referenceNumber
    };

    amlLogger.info(`Sumsub AML result for reference number ${referenceNumber}`);
    return Promise.resolve(sumsubData);
  } catch (error: any) {
    amlLogger.error(`ERROR_REQUEST_SUMSUB: ${error.message}`);
    throw new Error(error.message);
  }
};

function generateSignature(ts: number, method: string, pathname: string, bodyData: string): string {
  // function generateSignature(ts: number, method: string, pathname: string, bodyData: string | FormData | null): string {
  const secretKey = process.env.SUMSUB_SECRET_KEY;
  if (!secretKey) {
    const errorMessage = 'SUMSUB_SECRET_KEY_NOT_DEFINED';
    amlLogger.error(errorMessage);
    throw new Error(errorMessage);
  }

  const signatureCrypto = crypto.createHmac('sha256', secretKey);
  signatureCrypto.update(ts + method.toUpperCase() + pathname);
  if (bodyData) {
    signatureCrypto.update(bodyData);
  }

  const dataToSign = `${ts}${method.toUpperCase()}${pathname}${bodyData ? bodyData : ''}`;

  const hmac = HmacSHA256(dataToSign, secretKey);
  return hmac.toString(encHex);
}

export async function sumsubFetchWrapper(pathname: string, method: string, data: any = null) {
  // export async function sumsubFetchWrapper(pathname: string, method: string, data: any): Promise<Response> {
  const apiUrl = process.env.SUMSUB_BASE_URL;
  if (!apiUrl) {
    const message = 'SUMSUB_BASE_URL_NOT_DEFINED';
    amlLogger.error(message);
    throw new Error(message);
  }

  const appToken = process.env.SUMSUB_APP_TOKEN;
  if (!appToken) {
    const message = 'SUMSUB_API_URL_NOT_DEFINED';
    amlLogger.error(message);
    throw new Error(message);
  }

  let requestBody = '';
  if (data instanceof FormData) {
    // Create an array to store the form data key-value pairs
    const formDataEntries: string[] = [];
    // Iterate over each entry in the FormData object
    data.forEach((value, key) => {
      // Add the key-value pair to the array
      formDataEntries.push(`${key}=${value}`);
    });
    // Join the array elements with '&' to create a query string
    requestBody = formDataEntries.join('&');
  } else if (data) {
    if (process.env.SUMSUB_AML_TRANSACTION_VERIFICATION_LEVEL) {
      data.metaData = { levelName: process.env.SUMSUB_AML_TRANSACTION_VERIFICATION_LEVEL };
    }
    requestBody = JSON.stringify(data);
  }

  const ts = Math.floor(Date.now() / 1000);
  const signature = generateSignature(ts, method, pathname, requestBody);
  let options = {
    method: method,
    headers: {
      'Content-Type': 'application/json',
      'X-App-Token': appToken,
      'X-App-Access-Sig': signature,
      'X-App-Access-Ts': ts.toString()
    }
  };
  if (requestBody) {
    // @ts-ignore
    options.body = requestBody;
  }

  amlLogger.info('Fetching response...');
  const response = await fetch(`${apiUrl}${pathname}`, options);
  return response;
}

export const sumsubCheckTransactionRequest = async (transactionData: SumsubRequestData) => {
  const pathname = '/resources/applicants/-/kyt/txns/-/data';
  const method = 'POST';
  return await sumsubFetchWrapper(pathname, method, transactionData);
};

export const getTransactionsInformation = async (transactionId: string) => {
  const pathname = `/resources/kyt/txns/-;data.txnId=${transactionId}/one`;
  const method = 'GET';
  return await sumsubFetchWrapper(pathname, method);
};

export const getApplicantInformation = async (applicantId: string) => {
  const pathname = `/resources/applicants/${applicantId}/one`;
  const method = 'GET';
  try {
    const response = await sumsubFetchWrapper(pathname, method);
    const responseJson = await response.json();
    return responseJson;
  } catch (error: any) {
    amlLogger.error(
      `Error getting applicant information from sumsub with applicantId: ${applicantId} has error: ${error.message}`
    );
    throw new Error(error.message);
  }
};

export const formDataToSend = (
  record: Transaction,
  direction: 'in' | 'out',
  senderType: 'individual' | 'company',
  receiverType: 'individual' | 'company'
): SumsubRequestData => {
  const receiverName = record.BENEFICIARY_NAME_AND_ADDRESS.split('\r')[0];
  const senderName = record.SENDER_NAME.split('\r')[0];

  const sender = {
    externalUserId: `${senderType[0]}-${record.SENDER_ACCOUNT_NUMBER}`,
    fullName: senderName,
    type: senderType, // For when we have information about the person can be { individual or company }
    institutionInfo: {
      name: direction === 'out' ? 'RYVYL EU EAD' : '', // TODO: will use sender bank name from Oracle database when we have it
      code: direction === 'out' ? 'TRUDBG21XXX' : record.SENDER_BIC,
      address: {
        country: direction === 'out' ? 'BGR' : countries.alpha2ToAlpha3(record.SENDER_BIC?.substring(4, 6) ?? '')
      }
    }
  };

  const receiver = {
    externalUserId: `${receiverType[0]}-${record.BENEFICIARY_ACCOUNT_NUMBER}`,
    fullName: receiverName,
    type: receiverType, // For when we have information about the person can be { individual or company }
    institutionInfo: {
      name: direction === 'in' ? 'RYVYL EU EAD' : record.BENEFICIARY_BANK_NAME?.split('\r')?.at(0),
      code: direction === 'in' ? 'TRUDBG21XXX' : record.BENEFICIARY_BIC,
      address: {
        country: direction === 'in' ? 'BGR' : countries.alpha2ToAlpha3(record.BENEFICIARY_BIC?.substring(4, 6) ?? '')
      }
    }
  };

  let dataToSend: SumsubRequestData = {
    txnId: `${senderType[0]}${receiverType[0]}-${record.REFERENCE_NUMBER.toString()}`,
    info: {
      direction: direction,
      amount: record.AMOUNT_ORIGINAL_CURRENCY,
      currencyCode: record.ORIGINAL_CURRENCY,
      currencyType: 'fiat',
      paymentDetails: record.REASON ?? ''
    },
    applicant: direction == 'in' ? receiver : sender,
    counterparty: direction == 'in' ? sender : receiver
  };

  return dataToSend;
};
