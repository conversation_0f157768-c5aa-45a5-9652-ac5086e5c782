import axios, { AxiosError } from 'axios';
import {
  NameVerificationRequest,
  NameVerificationResponse,
  RequesterResponse,
  RyvylCopRequest
} from '../interfaces/technoXander';
import { txLogger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

const options = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: '',
    'x-fapi-financial-id': '',
    'x-fapi-interaction-id': '',
    'cop-requester-id': '',
    Accept: 'application/json'
  }
};

function updateOptions(contentType: string, token: string = '') {
  options.headers['Content-Type'] = contentType;

  if (contentType === 'application/json') {
    options.headers.Authorization = `Bearer ${token}`;
    options.headers['x-fapi-financial-id'] = process.env.TECHNOXANDER_REQUESTER_FINANCIAL_ID!;
    options.headers['x-fapi-interaction-id'] = uuidv4();
    options.headers['cop-requester-id'] = process.env.COP_REQUESTER_ID!;
    options.headers['Accept'] = 'application/json';
  }
}

async function getTechnoXanderToken() {
  try {
    const data: any = {
      grant_type: 'client_credentials',
      client_id: process.env.TECHNOXANDER_CLIENT_ID,
      client_secret: process.env.TECHNOXANDER_CLIENT_SECRET
    };
    const urlSearchParams = new URLSearchParams(data).toString();
    updateOptions('application/x-www-form-urlencoded');

    const response = await axios.post(
      `${process.env.TECHNOXANDER_REQUESTER_AUTH_URL!}/realms/AuthRealm/protocol/openid-connect/token`,
      urlSearchParams,
      options
    );

    return response.data.access_token;
  } catch (error) {
    if (axios.isAxiosError(error)) txLogger.error(`Axios Error: ${error.response?.data || error.message}`);
    else txLogger.error(`Unexpected Error: ${error}`);

    throw error;
  }
}

async function requestNameVerification(
  token: string,
  data: NameVerificationRequest
): Promise<NameVerificationResponse> {
  updateOptions('application/json', token);

  try {
    const response = await axios.post(
      `${process.env.TECHNOXANDER_REQUESTER_API_URL!}/open-banking/v3.3/cop/confirmation-payee-requester/request-name-verification`,
      data,
      options
    );

    return response.data as NameVerificationResponse;
  } catch (error: any) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      const errorResponse = axiosError.response?.data as NameVerificationResponse;

      return errorResponse;
    } else {
      txLogger.error(error);
      throw new Error(error);
    }
  }
}

export async function verifyEntityValidity(data: RyvylCopRequest): Promise<RequesterResponse> {
  const nameVerificationRequest: NameVerificationRequest = {
    Data: {
      SchemeName: 'SortCodeAccountNumber',
      AccountType: data.accountType.toLowerCase() === 'individual' ? 'Personal' : 'Business',
      Name: data.name,
      Identification: data.accountNumber.slice(-14),
      SecondaryIdentification:
        data.secondaryIdentification && data.secondaryIdentification.trim() !== ''
          ? data.secondaryIdentification
          : undefined
    }
  };

  const response = await performNameVerification(nameVerificationRequest);

  if (response.Errors || response.Details) {
    let errorMessage = 'An error occurred during name verification. Please contact support for more information.';
    (response.Errors ?? response.Details)?.forEach((error) => {
      txLogger.error(`${error.ErrorCode} - ${error.Message}`);
      switch (error.Message) {
        case 'Secondary Identification is mandatory':
          // secondary identification is optional but if the beneficiary has it, it needs to be passed mandatorily
          errorMessage = `Secondary Identification is mandatory for beneficiary ${data.name}`;
          break;
        case 'Missing general sort code.':
          // in this case beneficiary bank's sort code is either not registered for CoP or is invalid
          errorMessage = `No actions required. Ensure the account number is correct before proceeding.`;
          break;
      }
    });
    throw new Error(errorMessage);
  }
  return statusMessage(data.accountNumber, response);
}

async function performNameVerification(
  nameVerificationRequest: NameVerificationRequest
): Promise<NameVerificationResponse> {
  const token = await getTechnoXanderToken();
  txLogger.info(`[${nameVerificationRequest.Data.Identification}] - Performing name verification...`);
  const response = await requestNameVerification(token, nameVerificationRequest);

  return response;
}

async function statusMessage(identification: string, response: NameVerificationResponse): Promise<RequesterResponse> {
  let requesterResponse: RequesterResponse;
  txLogger.info(`[${identification}] - ${response.Message}`);

  try {
    if (response.Data.VerificationReport.Matched) {
      requesterResponse = {
        match: true,
        message: `Beneficiary information is correct`
      };
    } else {
      requesterResponse = {
        match: false,
        message: ''
      };
      switch (response.Data.VerificationReport.ReasonCode) {
        case 'ANNM': {
          requesterResponse.message = `Name is not the same as the name held on the account.`;
          break;
        }
        case 'IVCR': {
          requesterResponse.message = `Unable to locate customer account based on the secondary identificator.`;
          break;
        }
        case 'ACNS': {
          requesterResponse.message =
            'Account type is not supported for validation.\rContact recipient for more information.';
          break;
        }
        case 'OPTO': {
          requesterResponse.message = `Account is restricted from validation.\rContact recipient for more information.`;
          break;
        }
        case 'CASS': {
          requesterResponse.message = `Recipient account has been switched.`;
          break;
        }
        case 'SCNS': {
          requesterResponse.message = `Account's ASPSP has received incorrect sort code which does not belong to them.`;
          break;
        }
        case 'AC01': {
          requesterResponse.message = `Account not found - incorrect account number.`;
          break;
        }
        case 'MBAM': {
          requesterResponse.message = `Name differs slightly. Did you mean ${response.Data.VerificationReport.Name}?`;
          break;
        }
        case 'PANM': {
          requesterResponse.message = `Recipient details refer to personal instead of business account.`;
          break;
        }
        case 'BANM': {
          requesterResponse.message = `Recipient details refer to business instead of personal account.`;
          break;
        }
        case 'PAMM': {
          requesterResponse.message = `Recipient details refer to personal instead of business account.\rName differs slightly. Did you mean ${response.Data.VerificationReport.Name}?`;
          break;
        }
        case 'BAMM': {
          requesterResponse.message = `Recipient details refer to business instead of personal account.\rName differs slightly. Did you mean ${response.Data.VerificationReport.Name}?`;
          break;
        }
        default: {
          requesterResponse.message = `Unknown error occurred during name verification.`;
        }
      }
    }

    txLogger.info(`[${identification}] - Match: ${requesterResponse.match}, Message; ${requesterResponse.message}`);
  } catch (error: any) {
    txLogger.error(`[${identification}] - ${error.message}`);
    requesterResponse = {
      match: false,
      message: 'Unknown error occurred during name verification.'
    };
  }
  return requesterResponse;
}
