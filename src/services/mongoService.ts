import { MongoTransactionInterface } from '../interfaces/mongoTransactionInterface';
import { sumsubTransactionWebhook } from '../interfaces/sumsubTransactionWebhook';
import { AmlData } from '../interfaces/AmlData';
import { VisaResult } from '../interfaces/visaResult';
import LnTransaction from '../models/mongo/lnTransactionSchema';
import VisaBalance from '../models/mongo/visaBalancesSchema';
import VisaInvalidTx from '../models/mongo/visaInvalidTxSchema';
import VisaTransaction from '../models/mongo/visaTransactionSchema';
import { AccountBalance, Payload, PayoutNotificationData } from '../types/visaTypes';
import logger from '../../submodules/ryvyl-commons/services/loggerService';
import { setLastCheckedAMLRecord, setLastCheckedVisaDirectionRecord } from './oracleService';
import RyvylCopClientModel from '../models/mongo/ryvylCopClientSchema';
import { RyvylCopClient } from '../interfaces/technoXander';

let isInitiateVisaTxStartingPoint = false;
let isInitiateAmlTxStartingPoint = false;

export async function AMLTransactionExistInMongo(referenceId: string, referenceNumber: string): Promise<boolean> {
  try {
    let checkedAMLTransaction = await LnTransaction.find({
      referenceId: referenceId,
      referenceNumber: referenceNumber
    }).exec();

    let checkedAMLTxByRefNum = await LnTransaction.find({
      referenceNumber: referenceNumber
    }).exec();

    const fullArray = checkedAMLTransaction.concat(checkedAMLTxByRefNum);

    return fullArray.length > 0;
  } catch (error) {
    logger.error(`ERROR_FETCHING_AML_TRANSFERS: ${error}`);
    return false;
  }
}

export async function visaCheckReferenceIdExist(referenceIdSet: Set<string>): Promise<Set<string>> {
  const referenceIdArray = Array.from(referenceIdSet);

  const existingTransactions = await VisaTransaction.find(
    {
      referenceId: { $in: referenceIdArray }
    },
    'referenceId'
  ).exec();

  const existingReferenceNumber: Set<string> = new Set(
    existingTransactions.map((transaction: any) => transaction.referenceId)
  );

  return existingReferenceNumber;
}

export async function insertFilteredTransactions(amlData: AmlData): Promise<boolean> {
  try {
    await LnTransaction.updateOne({ uniqueId: amlData.uniqueId }, { $setOnInsert: amlData }, { upsert: true });
  } catch (error) {
    logger.error(`ERROR_UPSERT_REFERENCE_NUMBER ${amlData.referenceNumber}:`, error);
    return false;
  }
  return true;
}

export async function updateTransactionsFromWebhook(data: sumsubTransactionWebhook): Promise<void> {
  const transaction = await AMLFindTransactionByRefNumber(data.referenceNumber);
  if (!transaction) {
    throw new Error(`Transaction with this reference number does not exist in MongoDB: ${data.referenceNumber}:`);
  }

  await LnTransaction.updateOne({ referenceNumber: data.referenceNumber }, { manualStatus: data.status });
}

export async function AMLFindTransactionByRefNumber(
  referenceNumber: string
): Promise<MongoTransactionInterface | null> {
  let transaction = await LnTransaction.findOne({
    referenceNumber: referenceNumber
  }).exec();

  return transaction;
}

export async function amlSetInsertedInOra(uniqueId: number): Promise<boolean> {
  try {
    await LnTransaction.updateOne({ uniqueId: uniqueId }, { amlInsertInOra: true });
    return true;
  } catch (error) {
    logger.error(`ERROR_UPDATING_INSERTED_IN_ORA ${uniqueId}:`, error);
    return false;
  }
}

export async function insertVisaTransactions(visaResult: VisaResult): Promise<boolean> {
  try {
    const existingDocument = await VisaTransaction.findOne({ uniqueId: visaResult.uniqueId });
    if (existingDocument) {
      await VisaTransaction.updateOne(
        { uniqueId: visaResult.uniqueId },
        {
          $set: {
            status: visaResult.status,
            visaStatus: visaResult.visaStatus,
            details: visaResult.visaMsg
          }
        }
      );
    } else {
      await VisaTransaction.create({
        uniqueId: visaResult.uniqueId,
        referenceId: visaResult.referenceId,
        referenceNumber: visaResult.referenceNumber,
        status: visaResult.status,
        visaStatus: visaResult.visaStatus,
        details: visaResult.visaMsg
      });
    }
  } catch (error) {
    logger.error(`ERROR_UPDATING_REFERENCE_ID ${visaResult.referenceId}:`, error);
    return false;
  }
  return true;
}

export async function initiateVisaTxStartingPoint() {
  // We want only when the server start, then to initiate starting point
  if (isInitiateVisaTxStartingPoint) {
    return;
  }
  try {
    const latestTransaction = await VisaTransaction.findOne().sort({ uniqueId: -1 }).exec();
    const latestInTransaction = await VisaInvalidTx.findOne().sort({ uniqueId: -1 }).exec();

    let uniqueId: number = 0;
    if (latestTransaction?.uniqueId) {
      uniqueId = latestTransaction.uniqueId;
    }

    if (latestInTransaction?.uniqueId) {
      if (latestInTransaction.uniqueId > uniqueId) {
        uniqueId = latestInTransaction.uniqueId;
      }
    }

    setLastCheckedVisaDirectionRecord(uniqueId);

    isInitiateVisaTxStartingPoint = true;
  } catch (error) {
    logger.error(`ERROR_INITIATING_VISA_TX_STARTING_POINT: ${error}`);
  }
}

export async function updateNotifiedVisaTx(payoutNotification: PayoutNotificationData): Promise<boolean> {
  try {
    const existingDocument = await VisaTransaction.findOne({ referenceId: payoutNotification.clientReferenceId });
    if (existingDocument) {
      await VisaTransaction.updateOne(
        { referenceId: payoutNotification.clientReferenceId },
        {
          $set: {
            visaStatus: payoutNotification.status,
            details: payoutNotification.details ?? ''
          }
        }
      );
    }
  } catch (error) {
    logger.error(`ERROR_UPDATING_REFERENCE_ID ${payoutNotification.clientReferenceId}:`, error);
    return false;
  }

  return true;
}

export async function insertVisaBalance(accountBalance: AccountBalance): Promise<boolean> {
  try {
    const existingDocument = await VisaBalance.findOne({ currencyCode: accountBalance.balance.currencyCode });
    if (existingDocument) {
      if (existingDocument.amount !== accountBalance.balance.amount) {
        await VisaBalance.updateOne(
          { currencyCode: accountBalance.balance.currencyCode },
          {
            $set: {
              amount: accountBalance.balance.amount,
              balanceTimestamp: accountBalance.balanceTimestamp,
              lastTransactionTimestamp: accountBalance.lastTransactionTimestamp
            }
          }
        );
      }
    } else {
      await VisaBalance.create({
        accountId: accountBalance.accountId,
        currencyCode: accountBalance.balance.currencyCode,
        amount: accountBalance.balance.amount,
        balanceTimestamp: accountBalance.balanceTimestamp,
        lastTransactionTimestamp: accountBalance.lastTransactionTimestamp
      });
    }
  } catch (error) {
    logger.error(`ERROR_FETCHING_BALANCE ${accountBalance.balance.currencyCode}:`, error);
    return false;
  }
  return true;
}

export async function insertInvalidTransaction(
  referenceId: string,
  message: string,
  payload: Payload,
  uniqueId: number
): Promise<boolean> {
  try {
    const existingDocument = await VisaInvalidTx.findOne({ referenceId: referenceId });
    if (existingDocument) {
      await VisaInvalidTx.updateOne(
        { referenceId: referenceId },
        {
          $set: {
            reason: message,
            payload: payload
          }
        }
      );
    } else {
      await VisaInvalidTx.create({
        referenceId: referenceId,
        reason: message,
        payload: payload,
        uniqueId: uniqueId
      });
    }
  } catch (error) {
    logger.error(`ERROR_FETCHING_INVALID_TRANSACTION ${payload.transactionDetail.clientReferenceId}:`, error);
    return false;
  }
  return true;
}

export async function resolveInvalidTransaction(referenceId: string): Promise<boolean> {
  try {
    const existingDocument = await VisaInvalidTx.findOne({ referenceId: referenceId });
    if (existingDocument) {
      await VisaInvalidTx.updateOne(
        {
          referenceId: referenceId
        },
        {
          $set: {
            resolved: true
          }
        }
      );
    }
  } catch (error) {
    logger.error(`ERROR_FETCHING_INVALID_TRANSACTION ${referenceId}}:`, error);
    return false;
  }
  return true;
}

export async function initiateAmlTxStartingPoint() {
  // We want only when the server start, then to initiate starting point
  if (isInitiateAmlTxStartingPoint) {
    return;
  }
  try {
    const latestTransaction = await LnTransaction.findOne().sort({ uniqueId: -1 }).exec();

    let uniqueId: number = 0;
    if (latestTransaction?.uniqueId) {
      uniqueId = latestTransaction.uniqueId;
    }

    setLastCheckedAMLRecord(uniqueId);

    isInitiateAmlTxStartingPoint = true;
  } catch (error) {
    logger.error(`ERROR_INITIATING_AML_TX_STARTING_POINT: ${error}`);
  }
}

export async function getCopClient(iban: string): Promise<RyvylCopClient | null> {
  try {
    const copClients = (await RyvylCopClientModel.findOne({ iban: iban }).exec()) as RyvylCopClient;
    return copClients;
  } catch (error) {
    logger.error(`ERROR_FETCHING_COP_CLIENTS: ${error}`);
    return null;
  }
}
