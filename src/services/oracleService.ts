import oracleDB from 'oracledb';
import { executeOracleQuery, executeOracleWriteQuery } from '../config/oracledb';
import { AmlData } from '../interfaces/AmlData';
import { Transaction } from '../interfaces/oracleTransactionInterface';
import { VisaResult } from '../interfaces/visaResult';
import { amlLogger, visaLogger } from '../utils/logger';
import HttpException from '../classes/HttpException';
import { CreateInboundTransaction } from '../interfaces/routesInterface';
import logger from '../../submodules/ryvyl-commons/services/loggerService';

oracleDB.outFormat = oracleDB.OUT_FORMAT_OBJECT;

export async function getTransactions() {
  const selectDataSQL = `SELECT * FROM (SELECT * FROM AML_CHECK_TRANSACTIONS) WHERE ROWNUM <= 2`;
  try {
    const selectedData = await executeOracleQuery(selectDataSQL);
    return selectedData;
  } catch {
    return [];
  }
}

export let lastCheckedAMLRecord: number = Number(process.env.FIRST_AML_CHECK) || 1013000;
export let lastCheckedVisaDirectionRecord: number = Number(process.env.FIRST_VISA_CHECK) || 1013000;

export function setLastCheckedAMLRecord(uniqueId: number, immediate: boolean = false) {
  if (immediate) {
    lastCheckedAMLRecord = uniqueId;
    return;
  }

  if (uniqueId > lastCheckedAMLRecord) lastCheckedAMLRecord = uniqueId;
}

export function setLastCheckedVisaDirectionRecord(uniqueId: number) {
  if ((uniqueId ?? 0) > lastCheckedVisaDirectionRecord) lastCheckedVisaDirectionRecord = uniqueId;
}

export async function getCompletedTransactionsByUniqueIdOracle(uniqueId: number): Promise<Transaction | null> {
  const query = `
      SELECT * FROM AML_CHECK_TRANSACTIONS WHERE UNIQUE_ID = :uniqueId 
      AND TRANSACTION_STATUS = 16
    `;

  const binds = { uniqueId: uniqueId };

  const transactionArray = (await executeOracleQuery(query, binds, amlLogger)) ?? [];
  const transaction: Transaction | null = transactionArray.length > 0 ? transactionArray[0] : null;

  return transaction;
}

export async function getCompletedTransactionsByIdOrNumberOracle(
  transactionId: string | number
): Promise<Transaction | null> {
  const query = `
  SELECT * FROM AML_CHECK_TRANSACTIONS 
  WHERE (REFERENCE_ID = :transactionId OR REFERENCE_NUMBER = :transactionId)
  AND TRANSACTION_STATUS = 16
`;

  const binds = { transactionId: transactionId };
  const transactionArray = (await executeOracleQuery(query, binds, amlLogger)) ?? [];
  const transaction: Transaction | null = transactionArray.length > 0 ? transactionArray[0] : null;
  return transaction;
}

export async function getTransactionsForAMLCheck(): Promise<Transaction[]> {
  const query = `
      SELECT * FROM AML_CHECK_TRANSACTIONS WHERE (TRANSACTION_TYPE LIKE 'OT' OR TRANSACTION_TYPE LIKE 'IT' OR TRANSACTION_TYPE LIKE 'OS')
      AND TRANSACTION_STATUS LIKE 3 
      AND UNIQUE_ID > :firstUniqueID ORDER BY UNIQUE_ID ASC
    `;
  const binds = { firstUniqueID: lastCheckedAMLRecord };

  const selectedData = (await executeOracleQuery(query, binds, amlLogger)) ?? [];

  // Transactions type IS with the flag for SEPA Instant will be recorded directly into status 6
  const queryISWithStatus6 = `
      SELECT * FROM AML_CHECK_TRANSACTIONS WHERE (TRANSACTION_TYPE LIKE 'IS')
      AND TRANSACTION_STATUS LIKE 6
      AND UNIQUE_ID > :firstUniqueID ORDER BY UNIQUE_ID ASC
    `;

  const selectedDataISWithStatus5 = (await executeOracleQuery(queryISWithStatus6, binds, amlLogger)) ?? [];

  const concatResult = [...selectedData, ...selectedDataISWithStatus5] as Transaction[];

  return Promise.resolve(concatResult?.sort((a, b) => a.UNIQUE_ID - b.UNIQUE_ID));
}

export async function checkIfTransactionExistForAState(referenceId: string): Promise<boolean> {
  amlLogger.info('Running checkIfTransactionExistForAState');

  const query = `
      SELECT * FROM AML_CHECK_TRANSACTIONS WHERE (TRANSACTION_STATUS > 3 OR TRANSACTION_STATUS = 2)
      AND TRANSACTION_STATUS != 29
      AND REFERENCE_ID like :referenceId 
      ORDER BY UNIQUE_ID ASC
    `;
  const binds = { referenceId: referenceId };

  let selectedData;

  try {
    selectedData = (await executeOracleQuery(query, binds, amlLogger)) ?? [];
  } catch {
    selectedData = [];
  }

  if (selectedData.length > 0) {
    return Promise.resolve(true);
  }

  return Promise.resolve(false);
}

//this will fetch all Visa ready transactions that are to be processed without the API
export async function getForVisaTransactions(): Promise<any[]> {
  const selectDataSQL = `
    SELECT * FROM AML_CHECK_TRANSACTIONS WHERE DIRECTION LIKE 'VISADIRECT'
    AND TRANSACTION_TYPE LIKE 'OT'
    AND TRANSACTION_STATUS LIKE 8
    AND CLIENT_NAME != 'PAXUM BANK LIMITED  '
    AND UNIQUE_ID > :uniqueId
    ORDER BY UNIQUE_ID DESC
    `;
  const binds = { uniqueId: lastCheckedVisaDirectionRecord };

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds, visaLogger)) ?? [];
  } catch {
    selectedData = [];
  }
  return Promise.resolve(selectedData);
}

//this will fetch only visa ready transfers that are to be processed through the API. We might have to use a list for the supported merchants later on, but this works for the current case.
export async function getForVisaAPITransactions(): Promise<any[]> {
  // yes, there are 2 empty spaces at the end of the CLIENT_NAME and yes, they need to be there, that's how the name is saved in the database.
  const selectDataSQL = `
    SELECT * FROM AML_CHECK_TRANSACTIONS WHERE DIRECTION LIKE 'VISADIRECT'
    AND TRANSACTION_TYPE LIKE 'OT'
    AND TRANSACTION_STATUS LIKE 8
    AND CLIENT_NAME = 'PAXUM BANK LIMITED  '
    AND UNIQUE_ID > :uniqueId
    ORDER BY UNIQUE_ID DESC
    `;
  const binds = { uniqueId: lastCheckedVisaDirectionRecord };

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds, visaLogger)) ?? [];
  } catch {
    selectedData = [];
  }
  return Promise.resolve(selectedData);
}

export async function getForVisaTransaction(referenceId: string): Promise<any[]> {
  const selectDataSQL = `
    SELECT * FROM AML_CHECK_TRANSACTIONS WHERE DIRECTION LIKE 'VISADIRECT' 
    AND TRANSACTION_TYPE LIKE 'OT' 
    AND TRANSACTION_STATUS LIKE 8
    AND REFERENCE_ID LIKE :referenceId
    ORDER BY UNIQUE_ID DESC
    `;

  const binds = { referenceId: referenceId };

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds, visaLogger)) ?? [];
  } catch {
    selectedData = [];
  }

  return Promise.resolve(selectedData);
}

export async function insertResultsDB(data: AmlData | VisaResult, modeCheck: Number): Promise<boolean> {
  logger.info(
    `InsertResultsDB for unique_id ${data.uniqueId}, reference_id - ${data.referenceId}, reference_number - ${data.referenceNumber} and status - ${data.status}`
  );
  const insertDataSQL = `
        INSERT INTO AML_RESULT_TRANS (
          UNIQUE_ID, MODE_CHECK, STATUS, REFERENCE_ID, CDENIED, DATETIME, ID_PAY
        ) VALUES (
          :uniqueId, :modeCheck, :status, :referenceId, '', '', ''
        )
      `;

  try {
    await executeOracleWriteQuery(insertDataSQL, {
      uniqueId: data.uniqueId,
      modeCheck: modeCheck,
      status: data.status,
      referenceId: data.referenceId
    });
    logger.info('Result sent to Oracle.');
    return Promise.resolve(true);
  } catch {
    logger.error('Unable to write data in Oracle database.');
    return Promise.reject(false);
  }
}

export async function getInboundTransactionsByUniqueId(uniqueId: number) {
  // Transactions type IS with the flag for SEPA Instant will be recorded directly into status 6
  const selectDataSQL = `
  SELECT * FROM AML_CHECK_TRANSACTIONS WHERE TRANSACTION_TYPE LIKE 'IS' 
  AND TRANSACTION_STATUS LIKE 6
  AND UNIQUE_ID > :uniqueId
  ORDER BY UNIQUE_ID ASC
  `;

  const binds = { uniqueId: uniqueId };

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds)) ?? [];
  } catch {
    selectedData = [];
  }

  return Promise.resolve(selectedData);
}

export async function getOutboundTransactionsByUniqueId(uniqueId: number, transactionType: string) {
  // Define the allowed values for transactionType
  const allowedTypes = ['IS', 'IT', 'II', 'OT', 'OS', 'IO'];

  const typeList = transactionType.split(',').map((t) => t.trim().toUpperCase());

  // Check if any value in typeList is not in the allowedTypes array
  const invalidTypes = typeList.filter((t) => !allowedTypes.includes(t));

  if (invalidTypes.length > 0) {
    throw new HttpException(400, `Invalid transaction type(s): ${invalidTypes.join(', ')}`);
  }

  // Dynamically create bind variables (:type0, :type1, :type2, etc.)
  const typeBinds = typeList.map((_, index) => `:type${index}`).join(',');

  const selectDataSQL = `
  SELECT * FROM AML_CHECK_TRANSACTIONS WHERE TRANSACTION_TYPE IN (${typeBinds})
  AND TRANSACTION_STATUS LIKE 3
  AND UNIQUE_ID > :uniqueId
  ORDER BY UNIQUE_ID ASC
  `;

  const binds: any = { uniqueId: uniqueId };
  typeList.forEach((type, index) => {
    binds[`type${index}`] = type;
  });

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds)) ?? [];
  } catch {
    selectedData = [];
  }

  return Promise.resolve(selectedData);
}

export async function getSuccessfullyTransactionsByUniqueId(uniqueId: number, transactionType: string) {
  // Define the allowed values for transactionType
  const allowedTypes = ['IS', 'IT', 'II', 'OT', 'OS', 'IO'];

  const typeList = transactionType.split(',').map((t) => t.trim().toUpperCase());

  // Check if any value in typeList is not in the allowedTypes array
  const invalidTypes = typeList.filter((t) => !allowedTypes.includes(t));

  if (invalidTypes.length > 0) {
    throw new HttpException(400, `Invalid transaction type(s): ${invalidTypes.join(', ')}`);
  }

  // Dynamically create bind variables (:type0, :type1, :type2, etc.)
  const typeBinds = typeList.map((_, index) => `:type${index}`).join(',');

  const selectDataSQL = `
  SELECT * FROM AML_CHECK_TRANSACTIONS WHERE TRANSACTION_TYPE IN (${typeBinds})
  AND TRANSACTION_STATUS LIKE 16
  AND UNIQUE_ID > :uniqueId
  ORDER BY UNIQUE_ID ASC
  `;

  const binds: any = { uniqueId: uniqueId };
  typeList.forEach((type, index) => {
    binds[`type${index}`] = type;
  });

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds)) ?? [];
  } catch {
    selectedData = [];
  }

  return Promise.resolve(selectedData);
}

export async function getAuthorizedTransactionsByUniqueIdForClient(
  uniqueId: number,
  transactionType: string,
  clientNumber: number
) {
  // Define the allowed values for transactionType
  const allowedTypes = ['IS', 'IT', 'II', 'OT', 'OS', 'IO'];

  const typeList = transactionType.split(',').map((t) => t.trim().toUpperCase());

  // Check if any value in typeList is not in the allowedTypes array
  const invalidTypes = typeList.filter((t) => !allowedTypes.includes(t));

  if (invalidTypes.length > 0) {
    throw new HttpException(400, `Invalid transaction type(s): ${invalidTypes.join(', ')}`);
  }
  // Transactions type IS with the flag for SEPA Instant will be recorded directly into status 6
  const hasIS = typeList.includes('IS');
  // Remove "IS" from typeList before binding
  const filteredTypeList = typeList.filter((t) => t !== 'IS');

  // Dynamically create bind variables (:type0, :type1, :type2, etc.)
  const typeBinds = filteredTypeList.map((_, index) => `:type${index}`).join(',');

  const selectDataSQL = `
    SELECT * FROM AML_CHECK_TRANSACTIONS 
    WHERE TRANSACTION_TYPE IN (${typeBinds})
    AND TRANSACTION_STATUS LIKE 5
    AND CLIENT_NUMBER LIKE :clientNumber
    AND UNIQUE_ID > :uniqueId
    ORDER BY UNIQUE_ID ASC
  `;

  const selectDataSQLForIS = `
  SELECT * FROM AML_CHECK_TRANSACTIONS 
  WHERE TRANSACTION_TYPE LIKE 'IS'
  AND TRANSACTION_STATUS LIKE 6
  AND CLIENT_NUMBER LIKE :clientNumber
  AND UNIQUE_ID > :uniqueId
  ORDER BY UNIQUE_ID ASC
`;

  const binds: any = {
    uniqueId: uniqueId,
    clientNumber: clientNumber
  };

  filteredTypeList.forEach((type, index) => {
    binds[`type${index}`] = type;
  });

  let selectedData: any[];
  try {
    const selectedData1 = (await executeOracleQuery(selectDataSQL, binds)) ?? [];
    let selectedData2;
    if (hasIS) {
      const bindsForIS = {
        uniqueId: uniqueId,
        clientNumber: clientNumber
      };
      selectedData2 = (await executeOracleQuery(selectDataSQLForIS, bindsForIS)) ?? [];
    }
    selectedData = [...selectedData1, ...(selectedData2 || [])] as Transaction[];
  } catch {
    selectedData = [];
    logger.error('Error when running query for authorized transactions');
  }

  return Promise.resolve(selectedData);
}

export async function getErrorTransactionsByUniqueId(
  uniqueId: number,
  transactionType: string,
  transactionStatus: number
) {
  // Define the allowed values for transactionType
  const allowedTypes = ['IS', 'IT', 'II', 'OT', 'OS', 'IO'];
  const typeList = transactionType.split(',').map((t) => t.trim().toUpperCase());

  // Check if any value in typeList is not in the allowedTypes array
  const invalidTypes = typeList.filter((t) => !allowedTypes.includes(t));

  if (invalidTypes.length > 0) {
    throw new HttpException(400, `Invalid transaction type(s): ${invalidTypes.join(', ')}`);
  }

  // Dynamically create bind variables (:type0, :type1, :type2, etc.)
  const typeBinds = typeList.map((_, index) => `:type${index}`).join(',');

  const selectDataSQL = `
  SELECT * FROM AML_CHECK_TRANSACTIONS WHERE TRANSACTION_TYPE IN (${typeBinds}) 
  AND TRANSACTION_STATUS LIKE :transactionStatus
  AND UNIQUE_ID > :uniqueId
  ORDER BY UNIQUE_ID ASC
  `;
  const binds: any = { uniqueId: uniqueId, transactionStatus: transactionStatus };
  typeList.forEach((type, index) => {
    binds[`type${index}`] = type;
  });

  let selectedData;
  try {
    selectedData = (await executeOracleQuery(selectDataSQL, binds)) ?? [];
  } catch {
    selectedData = [];
  }

  return Promise.resolve(selectedData);
}

// Method that gets the latest unique id of the given table name
export async function getLatestTransactionUniqueIdOraSys(tableName: string) {
  const selectMaxUniqueIdSQL = `
    SELECT MAX(UNIQUE_ID) AS LATEST_UNIQUE_ID FROM ${tableName}
  `;

  let latestUniqueId = null;
  try {
    const result = (await executeOracleQuery(selectMaxUniqueIdSQL, null, logger)) ?? [];
    latestUniqueId = result.length > 0 ? result[0].LATEST_UNIQUE_ID : null;
  } catch {
    latestUniqueId = null;
  }

  return Promise.resolve(latestUniqueId);
}

// Method that inserts given inbound transaction into AML_IMP_FOREIGN_PAYMENTS table in the OracleDB
export async function insertInboundTransaction(transactionData: CreateInboundTransaction) {
  let latestUniqueId = await getLatestTransactionUniqueIdOraSys('AML_IMP_FOREIGN_PAYS');
  if (!latestUniqueId) {
    throw new Error('Unable to fetch latest unique id from the db. Please try again later.');
  }

  try {
    latestUniqueId = Number(latestUniqueId);

    const binds = {
      uniqueId: latestUniqueId + 1,
      valueDate: transactionData.valueDate,
      referenceNumber: transactionData.referenceNumber,
      transactionDate: transactionData.transactionDate,
      amount: transactionData.amount,
      currency: transactionData.currency,
      beneficiaryNameAndAddress: transactionData.beneficiaryNameAndAddress,
      beneficiaryBic: transactionData.beneficiaryBic,
      beneficiaryAccountNumber: transactionData.beneficiaryAccountNumber,
      senderName: transactionData.senderName,
      senderAddress: transactionData.senderAddress,
      senderBic: transactionData.senderBic,
      senderAccountNumber: transactionData.senderAccountNumber,
      senderBankName: transactionData.senderBankName,
      senderBankCountry: transactionData.senderBankCountry,
      reason: transactionData.reason,
      direction: transactionData.direction,
      idPay: transactionData.idPay
    };

    const insertTransactionQuery = `
      INSERT INTO AML_IMP_FOREIGN_PAYS (
        UNIQUE_ID, VALUE_DATE, REFERENCE_NUMBER, TRANSACTION_DATE, AMOUNT, CURRENCY, BENEFICIARY_NAME_AND_ADDRESS,
        BENEFICIARY_BIC, BENEFICIARY_ACCOUNT_NUMBER, SENDER_NAME, SENDER_ADDRESS, SENDER_BIC, SENDER_ACCOUNT_NUMBER,
        SENDER_BANK_NAME, SENDER_BANK_COUNTRY, REASON, DIRECTION, ID_PAY
      ) VALUES (
        :uniqueId, :valueDate, :referenceNumber, :transactionDate, :amount, :currency,
        :beneficiaryNameAndAddress, :beneficiaryBic, :beneficiaryAccountNumber,
        :senderName, :senderAddress, :senderBic, :senderAccountNumber, :senderBankName, :senderBankCountry,
        :reason, :direction, :idPay
      )
    `;

    await executeOracleWriteQuery(insertTransactionQuery, binds);
    logger.info('Inbound transaction sent to Oracle.');
    return Promise.resolve(true);
  } catch (err) {
    throw err;
  }
}
