import fs from 'fs';
import https from 'https';
import path from 'path';
import config from '../config/visa';
import jose from 'node-jose';
import request from 'request';
import { Transaction } from '../interfaces/oracleTransactionInterface';
import { AIClassifierLogger, visaLogger } from '../utils/logger';
import * as VisaType from '../types/visaTypes';
import axios, { AxiosInstance } from 'axios';
import { PaxumVisaTransactionInterface } from '../interfaces/paxumVisaTransactionInterface';
import { CustomError } from '../utils/CustomError';
import { classifyCustomerType } from './aiCustomerClassifier';

const getOptions = async (): Promise<VisaType.RequestOptions> => {
  if (!config.key || !config.cert) {
    throw new Error('Visa PK and Cert are not available');
  }

  const authToken = Buffer.from(config.username + ':' + config.password).toString('base64');
  const headers: VisaType.Headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    Authorization: 'Basic ' + authToken,
    keyId: config.mleKeyId
  };

  const options: VisaType.RequestOptions = {
    hostname: config.hostname,
    port: config.port,
    key: fs.readFileSync(path.resolve(__dirname, config.key)),
    cert: fs.readFileSync(path.resolve(__dirname, config.cert)),
    ca: fs.readFileSync(path.resolve(__dirname, config.ca)),
    headers: headers,
    json: true,
    uri: '',
    method: '',
    body: undefined
  };

  options.agent = new https.Agent(options);

  return options;
};

const getAxiosInstance = async (options: VisaType.RequestOptions): Promise<AxiosInstance> => {
  const axiosConfig = {
    baseURL: `https"//${options.hostname}:${options.port}`,
    httpsAgent: new https.Agent(options),
    headers: options.headers
  };
  return axios.create(axiosConfig);
};

export const getVisaDirectConfiguredOptions = async (
  key: string,
  method: string,
  payload: VisaType.Payload | undefined,
  cancelQueryData?: VisaType.CancelQuery | undefined,
  query?: any
): Promise<VisaType.RequestOptions> => {
  const options = await getOptions();

  options.headers!.keyId = config.mleKeyId;
  let uri = process.env.VISA_API_PAYOUT;

  switch (key) {
    case 'QUERY_PAYOUT': {
      uri = `${uri}?idType=CLIENT_REFERENCE_ID&initiatingPartyId=${payload?.transactionDetail.initiatingPartyId}&id=`;
      break;
    }
    case 'VALIDATE_PAYOUT': {
      uri = `${uri}/validate`;
      break;
    }
    case 'ACCOUNT_BALANCES': {
      uri = `${uri}/accountBalance?initiatingPartyId=${query.initiatingPartyId}`;
      break;
    }
    case 'ACCOUNT_BALANCE': {
      uri = `${uri}/accountBalance?initiatingPartyId=${payload?.transactionDetail.initiatingPartyId}&currencyCode=`;
      break;
    }
    case 'POSTING_CALENDAR': {
      uri = `${uri}/postingCalendar?initiatingPartyId=${payload?.transactionDetail.initiatingPartyId}&recipientBankCountryCode=`;
      break;
    }
    case 'CANCEL_TRANSACTION': {
      if (cancelQueryData) {
        uri = `${uri}?id=${cancelQueryData.id}&initiatingPartyId=${cancelQueryData.initiatingPartyId}&idType=${cancelQueryData.idType}`;
      }
      break;
    }
  }

  if (typeof uri === 'undefined') {
    return Promise.reject(`${uri}_NOT_DEFINED`);
  }

  options.uri = uri;
  options.method = method;

  if (payload) {
    const parameters = await getBaseParameters(payload);
    let keystore = jose.JWK.createKeyStore();
    let encProps = {
      kid: config.mleKeyId,
      alg: 'RSA-OAEP-256',
      enc: 'A128GCM'
    };
    let encryptionCert = fs.readFileSync(config.mlePublicKeyPath);
    let keyNew = await keystore.add(encryptionCert, 'pem', encProps);

    let encryptedDataNew = await jose.JWE.createEncrypt(
      {
        format: 'compact',
        fields: {
          enc: 'A128GCM',
          iat: Date.now()
        }
      },
      keyNew
    )
      .update(JSON.stringify(parameters.payload))
      .final();

    options.body = { encData: encryptedDataNew };
  }

  return options;
};

const currencyToCountryMap: VisaType.CurrencyCountryMap = {
  BRL: 'BRA', // Brazil
  COP: 'COL', // Colombia
  PHP: 'PHL', // Philippines
  AUD: 'AUS', // Australia
  CAD: 'CAN', // Canada
  SGD: 'SGP', // Singapore
  ZAR: 'ZAF', // South Africa
  THB: 'THA', // Thailand
  IDR: 'IDN', // Indonesia
  INR: 'IND', // India
  AED: 'ARE', // United Arab Emirates
  TRY: 'TUR', // Turkey
  MXN: 'MEX', // Mexico
  GBP: 'GBR', // United Kingdom
  HKD: 'HKG', // Hong Kong Special Administrative Region
  // TODO: phase 4
  CNY: 'CHN', // China
  USD: 'USA', // United States of America
  KRW: 'KOR', // KOREA (THE REPUBLIC OF)
  MYR: 'MYS', // MALAYSIA
  CRC: 'CRI', // COSTA RICA
  CLP: 'CHL', // CHILE
  // TODO: phase 5
  JPY: 'JPN', // JAPAN
  PEN: 'PRY', // PERU
  KES: 'KEN', // KENYA
  ILS: 'ISR', // ISRAEL
  PKR: 'PAK' // PAKISTAN
};

function getCountryCodeByCurrency(currencyCode: string): string {
  return currencyToCountryMap[currencyCode] || 'Unknown';
}

function isFromState(value: string): boolean {
  return value.includes('CAN') || value.includes('USA') || value.includes('CANADA');
}

export function extractPayloadDetails(record: Transaction): VisaType.RecordDetails {
  // Transaction Detail fields
  let purposeOfPayment: string | undefined = undefined;
  let statementNarrative: string = '';

  // Recipient Detail fields
  let idNumber: string | undefined = undefined;
  let idType: string | undefined = undefined;
  let idIssueCountry: string | undefined = undefined;
  let branchCode: any | undefined = undefined;
  let accountType: string | undefined = '1';
  let bankCode: string | number = '';
  let bankName: string | undefined = '';
  let city: string = '';
  let state: string | undefined = undefined;
  let postalCode: string | undefined = undefined;
  let contactEmail: string | undefined = undefined;
  let bic: string | undefined = undefined;
  let contactNumber: string | undefined = undefined;
  let additionalData: VisaType.AdditionalData | undefined = undefined;
  let identificationList: VisaType.IdentificationList | undefined = undefined;
  let currencyCode: string | undefined = record.ORIGINAL_CURRENCY;
  let accountNumberType: string | undefined = undefined;
  let contactNumberType: string | undefined = undefined;
  let accountNumber = record.BENEFICIARY_ACCOUNT_NUMBER;
  const clientNumber: number | undefined = record.CLIENT_NUMBER;
  const transactionAmount: number = record.AMOUNT_ORIGINAL_CURRENCY || 0;

  // SPLIT EXTRACTED DATA TO PARTS
  // Extract name and address of beneficiary
  const beneficiaryData = record.BENEFICIARY_NAME_AND_ADDRESS.split('\r');
  const beneficiaryNames = beneficiaryData[0];
  const beneficiaryAddressLine1 = beneficiaryData[1];
  const beneficiaryAddressLine2 = beneficiaryData.length === 4 ? beneficiaryData[2] : undefined;
  const beneficiaryRegionDetails = beneficiaryData[beneficiaryData.length === 4 ? 3 : 2]?.split(',');
  const beneficiaryCity = beneficiaryRegionDetails?.at(0)?.trim() ?? '';
  const beneficiaryPostalCode = beneficiaryRegionDetails?.at(1)?.trim() ?? '';
  const beneficiaryCountry = beneficiaryRegionDetails?.at(2)?.trim();
  const beneficiaryState = isFromState(record.BENEFICIARY_NAME_AND_ADDRESS)
    ? (beneficiaryRegionDetails?.at(3)?.trim() ?? '')
    : undefined;

  const senderData = record.SENDER_NAME.split('\r');
  let senderNames = senderData[0];

  let senderAddressLine1 = senderData[1];
  let senderAddressLine2: string | undefined = undefined;
  let senderPostalCode: string = '';
  let senderCity: string = '';
  let senderCountry: string = '';
  let senderState: string | undefined = undefined;
  const senderAccountNumber = record.SENDER_ACCOUNT_NUMBER;

  if (senderNames === 'PAXUM BANK LIMITED' || record.CLIENT_NUMBER === 6295) {
    senderNames = 'FUNDSTR UAB';
    senderAddressLine1 = 'LVIVO 105 A';
    senderAddressLine2 = undefined;
    senderPostalCode = '08104';
    senderCity = 'VILNIUS';
    senderCountry = 'LTU';
    senderState = undefined;
  } else {
    senderAddressLine1 = senderData[1];
    senderAddressLine2 = senderData?.at(2);
    const senderRegionDetails = senderData[senderData.length === 4 ? 3 : 2]?.split(',');
    senderPostalCode = senderRegionDetails?.at(1)?.trim() ?? '';
    senderCity = senderRegionDetails?.at(0)?.trim() ?? '';
    senderCountry = senderRegionDetails?.at(2)?.trim() ?? '';
    senderState = isFromState(record.SENDER_NAME) ? (senderRegionDetails?.at(3)?.trim() ?? '') : undefined;
  }

  // Beneficiary bank name parts
  const bankNameParts = record.BENEFICIARY_BANK_NAME?.split('\r');

  // Reason parts
  const reasonParts = record?.REASON?.replace(`${senderNames} `, '').split(',') ?? [];
  const recipientType = reasonParts[0];
  let senderType = 'C';

  const timestampId = Date.now().toString().substr(-13);
  let countryCode = getCountryCodeByCurrency(record.ORIGINAL_CURRENCY);

  switch (countryCode) {
    case 'BRA': {
      idType = `Tax ID "T"`;
      statementNarrative = 'Funds transfer';
      purposeOfPayment = 'ISSCVE';
      idNumber = reasonParts.at(1)?.trim() || '';
      accountType = reasonParts.at(3)?.trim() || '1';
      bankCode = record.BANK_CODE || '';
      branchCode = reasonParts.at(2)?.trim() || '';
      bankName = bankNameParts?.at(0)?.trim();
      identificationList = [
        {
          idType: 'T',
          idNumber: reasonParts?.at(1)?.trim()
        }
      ];
      city = beneficiaryCity;
      break;
    }
    case 'COL': {
      statementNarrative = 'Funds transfer';
      purposeOfPayment = 'ISSCVE';
      idNumber = reasonParts.at(2)?.trim() || '';
      idIssueCountry = reasonParts.at(3)?.trim() || '';
      bankCode = record.BANK_CODE ?? '';
      bankName = bankNameParts?.at(0)?.trim();
      contactEmail = reasonParts.at(4)?.trim() || '';
      city = beneficiaryCity;
      accountNumber = accountNumber.replace(/-/g, '');
      identificationList = [
        {
          idType: reasonParts?.at(1)?.trim() ?? 'L',
          idNumber: reasonParts?.at(2)?.trim(),
          idIssueCountry: reasonParts?.at(3)?.trim()
        }
      ];
      if (idType === "Diplomatic ID 'D'")
        additionalData = [
          {
            name: beneficiaryNames,
            value: idNumber
          }
        ];
      break;
    }
    case 'PHL': {
      statementNarrative = 'Funds transfer';
      purposeOfPayment = '**********';
      bankCode = record.BANK_CODE ?? '';
      bankName = bankNameParts?.at(0);
      city = beneficiaryCity;
      break;
    }
    case 'CAN': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = '420';
      postalCode = beneficiaryPostalCode;
      city = beneficiaryCity;
      state = beneficiaryState;
      bankCode = record.BANK_CODE ?? '';
      branchCode = reasonParts.at(1)?.trim() ?? '';
      bankName = bankNameParts?.at(0)?.trim() ?? '';
      break;
    }
    case 'AUS': {
      statementNarrative = 'Funds Transfer';
      city = beneficiaryCity;
      bankCode = record.BANK_CODE ?? '';
      branchCode = reasonParts.at(1)?.trim();
      bankName = bankNameParts?.at(0)?.trim();
      break;
    }
    case 'SGP': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = 'ISOTHR';
      city = beneficiaryCity;
      bic = record.BENEFICIARY_BIC;
      bankName = bankNameParts?.at(0);
      break;
    }
    case 'ZAF': {
      statementNarrative = 'Funds Transfer';
      contactNumber = reasonParts.at(1)?.trim();
      branchCode = reasonParts.at(2)?.trim();
      bankName = bankNameParts?.at(0)?.trim();
      city = beneficiaryCity;
      contactNumberType = 'MOBILE';
      contactEmail = reasonParts.at(3)?.trim() || '';
      break;
    }
    case 'THA': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = 'SCVE';
      city = beneficiaryCity;
      bic = record.BENEFICIARY_BIC;
      bankName = bankNameParts?.at(0);
      accountNumber = accountNumber.replace(/-/g, '');
      break;
    }
    case 'IDN': {
      statementNarrative = 'Funds Transfer';
      city = beneficiaryCity;
      bankCode = record.BANK_CODE ?? '';
      branchCode = reasonParts.at(1)?.trim();
      bankName = bankNameParts?.at(0)?.trim();
      accountType = undefined;
      break;
    }
    case 'IND': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = 'P1004'; //Legal services
      bankCode = record.BANK_CODE ?? '';
      bankName = bankNameParts?.at(0);
      city = beneficiaryCity;
      break;
    }
    case 'ARE': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = 'TOF'; //Transfer of funds between persons Normal and Juridical
      accountNumberType = 'IBAN';
      city = beneficiaryCity;
      bankName = bankNameParts?.at(0);
      bankCode = record.BANK_CODE ?? '';
      break;
    }
    case 'TUR': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = '99'; //Other Payments
      accountNumberType = 'IBAN';
      bankName = bankNameParts?.at(0);
      bankCode = record.BANK_CODE ?? '';
      break;
    }
    case 'MEX': {
      statementNarrative = 'Funds Transfer';
      purposeOfPayment = 'ISUBIL'; // Payment to common utility provider
      accountNumberType = 'DEFAULT';
      city = beneficiaryCity;
      bankName = bankNameParts?.at(0);
      break;
    }
    case 'GBR': {
      statementNarrative = 'Funds Transfer';
      accountNumberType = 'DEFAULT';
      bankCode = record.BANK_CODE ?? '';
      bankName = bankNameParts?.at(0);
      break;
    }
    case 'HKG': {
      statementNarrative = 'Funds Transfer';
      accountNumberType = 'DEFAULT';
      bankCode = record.BANK_CODE ?? '';
      branchCode = reasonParts.at(1)?.trim();
      bankName = bankNameParts?.at(0)?.trim();
      city = beneficiaryCity;
      break;
    }
  }

  return {
    beneficiaryNames,
    beneficiaryFirstName: undefined,
    beneficiaryLastName: undefined,
    beneficiaryAddressLine1,
    beneficiaryAddressLine2,
    beneficiaryCountry,
    senderNames,
    senderAddressLine1,
    senderAddressLine2,
    timestampId,
    countryCode,
    purposeOfPayment,
    statementNarrative,
    branchCode,
    accountType,
    bankCode,
    city,
    bankName,
    state,
    postalCode,
    contactEmail,
    bic,
    contactNumber,
    senderCity,
    senderPostalCode,
    senderCountry,
    senderState,
    recipientType,
    senderType,
    identificationList,
    additionalData,
    accountNumberType,
    accountNumber,
    contactNumberType,
    currencyCode,
    senderAccountNumber,
    clientNumber,
    transactionAmount
  };
}

export async function getBaseParameters(payload: VisaType.Payload | VisaType.CancelQuery) {
  return {
    'x-client-transaction-id': '****************',
    Accept: 'application/json',
    'Content-Type': 'application/json',
    payload: payload
  };
}

export async function validatePayout(payload: VisaType.Payload, from?: string): Promise<VisaType.ValidateResult> {
  payload.transactionDetail.clientReferenceId = undefined;

  try {
    const options = await getVisaDirectConfiguredOptions('VALIDATE_PAYOUT', 'POST', payload);

    return await new Promise(async (resolve, reject) => {
      request.post(options, async (error: Error, response: request.Response) => {
        if (error) {
          reject(new Error(error.message));
          return;
        }

        if (!response || !response.statusCode) {
          reject(new Error('NO_RESPONSE_NO_STATUS_CODE'));
          return;
        }

        if (response.statusCode === 500) {
          const message = response.body?.errorResponse?.message ?? 'VISA_SERVER_ERROR';
          reject(new Error(message));
          return;
        }

        if (response.body?.encData) {
          try {
            const decryptKey = await jose.JWK.asKey(fs.readFileSync(config.mlePrivateKeyPath), 'pem');
            const decryptedResult = await jose.JWE.createDecrypt(decryptKey).decrypt(response.body.encData);
            const decryptedData = JSON.parse(String(decryptedResult.plaintext));

            visaLogger.info(`Status: ${response.statusCode}`);
            visaLogger.info(`Result: ${JSON.stringify(decryptedData)}`);

            switch (response.statusCode) {
              case 200:
                resolve(decryptedData);
                break;

              case 400:
                resolve({
                  validationResultCode: 'INVALID',
                  validationDetails: [decryptedData.errorResponse]
                });
                break;

              case 401:
                reject(new Error('Unauthorized request.'));
                break;

              case 403:
                reject(new Error('Forbidden request.'));
                break;

              case 429:
                reject(new Error('Too many requests. Please try again later.'));
                break;

              case 503:
                reject(new Error('Service unavailable. Please try again later.'));
                break;

              default:
                reject(
                  new Error(
                    decryptedData?.errorResponse
                      ? JSON.stringify(decryptedData.errorResponse)
                      : `Unexpected status code: ${response.statusCode}`
                  )
                );
            }
          } catch (decryptError) {
            reject(new Error('Decryption failed'));
          }
        } else {
          reject(new Error('NO_ENCRYPTED_DATA'));
        }
      });
    });
  } catch (error: any) {
    visaLogger.error(`validatePayout error: ${error.message}`);

    return {
      validationResultCode: 'ERROR',
      validationDetails: [error.message]
    } as VisaType.ValidateResult;
  }
}

export async function queryPayout(referenceId: string): Promise<VisaType.PayoutDetails> {
  const options = await getVisaDirectConfiguredOptions('QUERY_PAYOUT', 'GET', undefined);
  const axiosInstance = await getAxiosInstance(options);
  const decryptKey = await jose.JWK.asKey(fs.readFileSync(config.mlePrivateKeyPath), 'pem');

  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosInstance.get(options.uri + referenceId);
      const decryptedResult = await jose.JWE.createDecrypt(decryptKey).decrypt(response.data.encData);
      const decryptedData: VisaType.PayoutDetails = JSON.parse(String(decryptedResult.plaintext));

      visaLogger.debug(`Status: ${response.status}`);
      visaLogger.debug(`Result: ${JSON.stringify(decryptedData)}`);
      resolve(decryptedData);
    } catch (error: any) {
      const decryptedError = await jose.JWE.createDecrypt(decryptKey).decrypt(error.response.data.encData);
      const errorMessage = JSON.parse(String(decryptedError.plaintext));
      visaLogger.error(`Error: ${JSON.stringify(errorMessage)}`);
      reject();
    }
  });
}

export async function getAccountBalances(): Promise<VisaType.AccountBalance[]> {
  // :TODO to make logic for initiating party_id
  const initiatingPartyId = process.env.VISA_INITIATING_PARTY_ID_FUNDSTR;
  const options = await getVisaDirectConfiguredOptions('ACCOUNT_BALANCES', 'GET', undefined, undefined, {
    initiatingPartyId: initiatingPartyId
  });
  const axiosInstance = await getAxiosInstance(options);
  const decryptKey = await jose.JWK.asKey(fs.readFileSync(config.mlePrivateKeyPath), 'pem');

  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosInstance.get(options.uri);
      const decryptedResult = await jose.JWE.createDecrypt(decryptKey).decrypt(response.data.encData);
      const decryptedData: VisaType.AccountBalance[] = JSON.parse(String(decryptedResult.plaintext));

      resolve(decryptedData);
    } catch (error: any) {
      let errorMessage;
      if (error.response?.data?.encData) {
        const decryptedResult = await jose.JWE.createDecrypt(decryptKey).decrypt(error.response.data.encData);
        errorMessage = JSON.parse(String(decryptedResult.plaintext));
      } else errorMessage = error.message;

      reject({ message: errorMessage });
    }
  });
}

function formatPayloadForVisaFromElcoin(payload: any) {
  if (!payload.transactionDetail) {
    return { status: 400, message: 'Field "transactionDetail" is required!' };
  }

  const initiatingPartyId = process.env.VISA_INITIATING_PARTY_ID_FUNDSTR;
  // :TODO to make logic for initiating party_id
  // recordDetails.senderNames === 'FUNDSTR UAB'
  //   ? Number(process.env.VISA_INITIATING_PARTY_ID_FUNDSTR)
  //   : Number(process.env.VISA_INITIATING_PARTY_ID_RYVYL);

  const businessApplicationId = 'FD';
  const senderSourceOfFunds = '01';
  const statementNarrative = 'Funds transfer';
  payload.transactionDetail = {
    ...payload.transactionDetail,
    businessApplicationId,
    senderSourceOfFunds,
    statementNarrative,
    initiatingPartyId
  };
  return payload;
}

export async function checkVidaDirectPayloadFromElcoin(payload: any) {
  let data = formatPayloadForVisaFromElcoin(payload);

  let validatedPayout = await validatePayout(data);

  if (validatedPayout.validationResultCode === 'INVALID' || validatedPayout.validationResultCode === 'ERROR') {
    visaLogger.debug(
      `Visa transaction with reference id ${data.transactionDetail.clientReferenceId} has invalid payout`
    );

    let message = '';
    if (validatedPayout.validationResultCode === 'INVALID') {
      message = validatedPayout
        .validationDetails!![0].details.map((detail) => `${detail.message} - ${detail.location}`)
        .join('\r\n');
    } else {
      message = JSON.stringify(validatedPayout.validationDetails!![0]);
    }

    return { status: 400, message: message };
  } else {
    return { status: 200, message: 'Payout is valid.' };
  }
}

export async function sendVisaDirectTransactionFromElcoin(payload: any) {
  let data = formatPayloadForVisaFromElcoin(payload);

  try {
    let visaResult = await sendToVisaTransaction(data);
    // :TODO - to save transaction on orasys and mongodb (now we are do not save because do not have unique_id and reference_number)
    return { statusCode: 200, message: 'Transaction successfully send to Visa.' };
  } catch (error: any) {
    visaLogger.error('sendVisaDirectTransactionFromElcoin:', error.message);
    return { statusCode: error.statusCode ? error.statusCode : 400, message: error.message };
  }
}

export async function sendToVisaTransaction(payload: VisaType.Payload): Promise<VisaType.SendTransactionResponse> {
  const options = await getVisaDirectConfiguredOptions('SEND_PAYOUT', 'POST', payload);

  try {
    return await new Promise(async (resolve, reject) => {
      request.post(options, async (err, response) => {
        if (err) {
          visaLogger.debug(`Error: ${err}`);
          return reject(new Error(err.message));
        }

        if (!response || !response.statusCode) {
          visaLogger.error('NO_RESPONSE_NO_STATUS_CODE');
          return reject(new Error('NO_RESPONSE_NO_STATUS_CODE'));
        }

        if (response.statusCode === 500) {
          const message = response.body?.errorResponse?.message ?? 'VISA_SERVER_ERROR';
          reject(new Error(message));
          return;
        }

        try {
          const decryptKey = await jose.JWK.asKey(fs.readFileSync(config.mlePrivateKeyPath), 'pem');
          const decryptedResult = await jose.JWE.createDecrypt(decryptKey).decrypt(response.body.encData);
          const decryptedData = JSON.parse(String(decryptedResult.plaintext));

          visaLogger.info(`VISA DIRECT STATUS CODE TRANSACTION: ${response.statusCode}`);
          switch (response.statusCode) {
            case 200:
            case 202:
              visaLogger.info(
                `Transaction with reference id ${decryptedData?.transactionDetail?.clientReferenceId} successfully sent to Visa, status of transaction ${decryptedData?.transactionDetail?.status}`
              );
              resolve({
                statusCode: response.statusCode,
                data: decryptedData
              });
              break;

            case 400:
              reject({ message: { statusCode: 400, message: extractVisaErrorDetailsMessage(decryptedData[0]) } });
              break;

            case 401:
            case 403:
              reject({
                message: {
                  statusCode: response.statusCode,
                  message: extractVisaErrorDetailsMessage(decryptedData.errorResponse)
                }
              });
              break;

            case 503:
              reject(new Error('Service unavailable. Please try again later.'));
              break;

            default:
              reject(
                new Error(
                  decryptedData?.errorResponse
                    ? JSON.stringify(decryptedData.errorResponse)
                    : `Unexpected status code: ${response.statusCode}`
                )
              );
          }
        } catch (decryptError) {
          visaLogger.error('Decryption failed', decryptError);
          reject(new Error('Decryption failed'));
        }
      });
    });
  } catch (error: any) {
    visaLogger.error(`UNEXPECTED_ERR_OCCURRED: ${JSON.stringify(error.message)}`);
    return Promise.reject(error.message);
  }
}

export function extractVisaErrorDetailsMessage(data: VisaType.ErrorRequestDetails): string {
  let message = '';
  if (!data.details || data.details.length == 0) {
    if (data.message) {
      message = data.message;
    } else {
      message = 'UNEXPECTED_ERR_OCCURRED!';
    }
    return message;
  }

  message = data?.details.map((detail) => `${detail.message} - ${detail.location}`).join('\r\n');

  return message;
}

export function extractPayloadDetailsFromPaxum(data: any): VisaType.AllNeededFieldsForVisa {
  const randomClientReferenceId = [...Array(16)].map(() => Math.random().toString(36)[2]).join('');

  let transactionUniqueId: string | undefined = data?.uniqueId;
  let purposeOfPayment: string | undefined = data?.additionalFields?.purposeOfPayment;
  let statementNarrative: string | undefined = undefined;
  let recipientCity: string | undefined = data?.additionalFields?.recipientCity;
  let recipientNames: string | undefined = data?.additionalFields?.recipientNames;
  let recipientFirstName: string | undefined = data?.additionalFields?.recipientFirstName;
  let recipientLastName: string | undefined = data?.additionalFields?.recipientLastName;
  let recipientAccountType: string | undefined = data?.recipientType;
  let recipientCountry: string | undefined = data?.additionalFields?.recipientCountry;
  let recipientState: string | undefined = data?.additionalFields?.recipientState;
  let recipientAddressLine1: string | undefined = data?.additionalFields?.recipientAddress1;
  let recipientAddressLine2: string | undefined = data?.additionalFields?.recipientAddress2;
  let recipientPostalCode: string | undefined = data?.additionalFields?.recipientPostCode;
  let recipientContactEmail: string | undefined = data?.additionalFields?.recipientContactEmail;
  let recipientIdNumber: string | undefined = data?.additionalFields?.recipientIdNumber;
  let recipientIdType: string | undefined = data?.additionalFields?.recipientIdType;
  let recipientIdIssueCountry: string | undefined = data?.additionalFields?.recipientIdIssueCountry;
  let recipientContactNumber: string | undefined = data?.additionalFields?.recipientContactNumber;
  let recipientContactNumberType: string | undefined = data?.additionalFields?.recipientContactNumberType;
  let recipientAccountNumber: string | undefined = data?.additionalFields?.recipientAccountNumber; // Add random randomClientReferenceId because from frontend they do not send a unequal ClientReferenceId
  let bic: string | undefined = data?.additionalFields?.bic;
  let branchCode: any | undefined = data?.additionalFields?.branchCode;
  let bankCode: string | number | undefined = data?.additionalFields?.bankCode;
  let bankName: string | undefined = data?.additionalFields?.bankName;
  let bankAccountNumberType: string | undefined = undefined;
  let currencyCode: string | undefined = data?.currencyCode;
  let transactionAmount: number | undefined = data?.amount;
  let senderClientNumber: number | undefined = undefined; // Coming from orasys
  let senderNames: string | undefined = 'PAXUM'; //temporary fix for the API. The check for paxum in the next method picks this up and add all necessary sender information.
  let senderFirstName: string | undefined = undefined;
  let senderLastName: string | undefined = undefined;
  let senderAddressLine1: string | undefined = undefined;
  let senderAddressLine2: string | undefined = undefined;
  let senderPostalCode: string | undefined = undefined;
  let senderCity: string | undefined = undefined;
  let senderCountry: string | undefined = undefined;
  let senderState: string | undefined = undefined;
  let senderAccountNumber: string | undefined = undefined;
  let senderAccountType: string | undefined = undefined;
  let additionalData: VisaType.AdditionalData | undefined = undefined;
  let identificationList: VisaType.IdentificationList | undefined = undefined;
  let clientReferenceId: string | undefined = data?.uniqueId ? data?.uniqueId : undefined;
  let countryCode: string | undefined = undefined;
  let bankAccountType: string | undefined = undefined;

  //TODO - Add logic for using different Initiating Party ID for different senders
  let initiatingPartyId: number | undefined = Number(process.env.VISA_INITIATING_PARTY_ID_FUNDSTR);

  return {
    transactionUniqueId,
    senderNames,
    senderFirstName,
    senderLastName,
    senderAddressLine1,
    senderAddressLine2,
    senderPostalCode,
    senderCity,
    senderCountry,
    senderState,
    senderAccountType,
    senderAccountNumber,
    recipientNames,
    recipientFirstName,
    recipientLastName,
    recipientAddressLine1,
    recipientAddressLine2,
    recipientPostalCode,
    recipientCity,
    recipientCountry,
    recipientState,
    recipientAccountType,
    recipientAccountNumber,
    recipientIdType,
    recipientIdNumber,
    recipientIdIssueCountry,
    recipientContactEmail,
    recipientContactNumber,
    recipientContactNumberType,
    bic,
    senderClientNumber,
    transactionAmount,
    bankName,
    branchCode,
    bankCode,
    currencyCode,
    purposeOfPayment,
    statementNarrative,
    bankAccountNumberType,
    additionalData,
    identificationList,
    clientReferenceId,
    countryCode,
    bankAccountType,
    initiatingPartyId
  };
}

async function additionalVisaFields(
  data: any,
  from: string,
  paxumTransaction?: PaxumVisaTransactionInterface
): Promise<VisaType.AllNeededFieldsForVisa> {
  let allVisaFields: VisaType.AllNeededFieldsForVisa;
  if (from == 'paxum') {
    allVisaFields = extractPayloadDetailsFromPaxum(data);
  } else if (from == 'orasys') {
    allVisaFields = await extractPayloadDetailsFromOrasys(data);
  } else if (from == 'orasys-paxum' && paxumTransaction) {
    allVisaFields = extractPayloadDetailsFromOrasysPaxum(data, paxumTransaction);
  } else {
    throw new Error('Not provided Processor Provider!');
  }

  if (!allVisaFields.currencyCode) {
    throw new Error('CurrencyCode is required!');
  }
  const countryCode = getCountryCodeByCurrency(allVisaFields.currencyCode);

  if (countryCode === 'Unknown') {
    throw new CustomError(400, 'The currencyCode is not allowed!');
  }

  // Replace sender details for visa sanctioned merchant
  if (allVisaFields.senderNames?.includes('PAXUM') || allVisaFields.senderClientNumber === 6295) {
    allVisaFields.senderNames = 'FUNDSTR UAB';
    allVisaFields.senderAddressLine1 = 'LVIVO 105 A';
    allVisaFields.senderPostalCode = '08104';
    allVisaFields.senderCity = 'VILNIUS';
    allVisaFields.senderCountry = 'LTU';
    allVisaFields.senderAccountNumber = '**********************';
    allVisaFields.senderAccountType = 'C';
    allVisaFields.initiatingPartyId = Number(process.env.VISA_INITIATING_PARTY_ID_FUNDSTR);
  } else {
    allVisaFields.initiatingPartyId = Number(process.env.VISA_INITIATING_PARTY_ID_RYVYL);
  }

  // Determine if the recipient account type is IBAN or Default
  const normalizedAccountNumber = allVisaFields.recipientAccountNumber?.toUpperCase() || '';
  const isRecipientIban = /^[A-Z]{2}\d{2}/.test(normalizedAccountNumber);
  if (isRecipientIban) {
    allVisaFields.bankAccountNumberType = 'IBAN';
  } else {
    allVisaFields.bankAccountNumberType = 'Default';
  }
  //

  allVisaFields.statementNarrative = 'Funds Transfer';
  allVisaFields.countryCode = countryCode;
  allVisaFields.recipientCountry = countryCode;
  switch (countryCode) {
    //Phase 1
    case 'CAN': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || '420';
      break;
    }
    //Phase 2
    case 'COL': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || 'ISSCVE';
      allVisaFields.bankAccountNumberType = 'DEFAULT';
      allVisaFields.identificationList = [
        {
          idType: allVisaFields.recipientIdType ?? 'L',
          idNumber: allVisaFields.recipientIdNumber,
          idIssueCountry: allVisaFields.recipientIdIssueCountry || allVisaFields.recipientCountry
        }
      ];
      if (allVisaFields.recipientIdType === "Diplomatic ID 'D'")
        allVisaFields.additionalData = [
          {
            name: allVisaFields.recipientNames,
            value: allVisaFields.recipientIdNumber
          }
        ];

      allVisaFields.bankAccountType = allVisaFields.bankAccountType ?? '1';
      break;
    }
    case 'IND': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || 'P0601'; //Legal services
      allVisaFields.bankAccountType = allVisaFields.bankAccountType ?? '1';
      break;
    }
    case 'IDN': {
      allVisaFields.bankAccountType = undefined;
      break;
    }
    case 'PHL': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || '**********';
      break;
    }
    case 'ZAF': {
      allVisaFields.recipientContactNumberType = allVisaFields.recipientContactNumberType
        ? allVisaFields.recipientContactNumberType
        : 'MOBILE';
      break;
    }
    case 'THA': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || 'SCVE';
      break;
    }

    // Phase 3
    case 'ARE': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || 'TOF'; //Transfer of funds between persons Normal and Juridical
      allVisaFields.bankAccountNumberType = 'IBAN';
      break;
    }
    case 'TUR': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || '99'; //Other Payments
      allVisaFields.bankAccountNumberType = 'IBAN';
      allVisaFields.identificationList = [
        {
          idType: allVisaFields.recipientIdType ?? 'L',
          idNumber: allVisaFields.recipientIdNumber,
          idIssueCountry: allVisaFields.recipientIdIssueCountry || allVisaFields.recipientCountry
        }
      ];
      break;
    }
    case 'MEX': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || 'ISUBIL'; // Payment to common utility provider
      allVisaFields.bankAccountNumberType = 'DEFAULT';
      break;
    }
    case 'BRA': {
      allVisaFields.purposeOfPayment = allVisaFields.purposeOfPayment || 'ISSCVE'; //PurchaseSaleOfServices
      allVisaFields.identificationList = [
        {
          idType: allVisaFields.recipientIdType ?? 'T',
          idNumber: allVisaFields.recipientIdNumber,
          idIssueCountry: allVisaFields.recipientIdIssueCountry || allVisaFields.recipientCountry
        }
      ];
      break;
    }
    case 'GBR': {
      //nothing to add!
      break;
    }
    case 'HKG': {
      allVisaFields.bankAccountNumberType = 'DEFAULT';
      break;
    }
  }

  return {
    ...allVisaFields
  };
}

export async function createPayload({
  data,
  from,
  paxumTransaction
}: {
  data: any;
  from: string;
  paxumTransaction?: PaxumVisaTransactionInterface;
}) {
  let recordDetails: VisaType.AllNeededFieldsForVisa = await additionalVisaFields(data, from, paxumTransaction);

  const recipientAddress: VisaType.Address = {
    addressLine1: recordDetails.recipientAddressLine1 || '',
    city: recordDetails.recipientCity || '',
    country: recordDetails.recipientCountry || '',
    ...(recordDetails.recipientAddressLine2 && { addressLine2: recordDetails.recipientAddressLine2 }),
    ...(recordDetails.recipientPostalCode && { postalCode: recordDetails.recipientPostalCode }),
    ...(recordDetails.recipientState && { state: recordDetails.recipientState })
  };

  const identificationList: VisaType.IdentificationList | undefined = recordDetails.identificationList;

  const additionalData: VisaType.AdditionalData | undefined = recordDetails.additionalData;

  const bank: VisaType.Bank = {
    accountNumberType: recordDetails.bankAccountNumberType ?? 'DEFAULT',
    accountNumber: recordDetails.recipientAccountNumber || '',
    BIC: recordDetails.bic,
    ...(recordDetails.bankCode && { bankCode: recordDetails.bankCode }), // Include this property only when we have recordDetails.bankCode
    accountName: recordDetails.recipientNames
      ? recordDetails.recipientNames
      : `${recordDetails.recipientFirstName} ${recordDetails.recipientLastName}`,
    branchCode: recordDetails.branchCode,
    accountType: recordDetails.bankAccountType,
    countryCode: recordDetails.countryCode || '',
    currencyCode: recordDetails.currencyCode || '',
    bankName: recordDetails.bankName
  };

  const recipientDetail: VisaType.RecipientDetail = {
    type: recordDetails.recipientAccountType,
    firstName: recordDetails.recipientFirstName,
    lastName: recordDetails.recipientLastName,
    name: recordDetails.recipientNames,
    contactNumber: recordDetails.recipientContactNumber,
    contactNumberType: recordDetails.recipientContactNumberType,
    contactEmail: recordDetails.recipientContactEmail,
    address: recipientAddress,
    identificationList: identificationList,
    additionalData: additionalData,
    bank: bank
  };

  const senderAddress: VisaType.Address = {
    country: recordDetails.senderCountry || '',
    city: recordDetails.senderCity || '',
    postalCode: recordDetails.senderPostalCode,
    state: recordDetails.senderState,
    addressLine1: recordDetails.senderAddressLine1 || '',
    addressLine2: recordDetails.senderAddressLine2
  };

  const senderDetail: VisaType.SenderDetail = {
    type: recordDetails.senderAccountType || '',
    firstName: recordDetails.senderFirstName,
    lastName: recordDetails.senderLastName,
    name: recordDetails.senderNames,
    senderAccountNumber: recordDetails.senderAccountNumber || '',
    address: senderAddress
  };

  const transactionDetails: VisaType.TransactionDetail = {
    initiatingPartyId: recordDetails.initiatingPartyId,
    businessApplicationId: 'FD',
    purposeOfPayment: recordDetails.purposeOfPayment,
    transactionAmount: recordDetails.transactionAmount || 0,
    transactionCurrencyCode: recordDetails.currencyCode || '',
    settlementCurrencyCode: recordDetails.currencyCode || '',
    clientReferenceId: recordDetails.clientReferenceId,
    senderSourceOfFunds: '01',
    statementNarrative: recordDetails.statementNarrative || ''
  };

  const payload: VisaType.Payload = {
    payoutMethod: 'B',
    senderDetail: senderDetail,
    recipientDetail: recipientDetail,
    transactionDetail: transactionDetails
  };

  return payload;
}

export async function extractPayloadDetailsFromOrasys(data: Transaction): Promise<VisaType.AllNeededFieldsForVisa> {
  // Sender information
  const senderData = data.SENDER_NAME.split('\r');
  // Beneficiary information
  const beneficiaryData = data.BENEFICIARY_NAME_AND_ADDRESS.split('\r');
  // Beneficiary bank name parts
  const bankNameParts = data.BENEFICIARY_BANK_NAME?.split('\r');

  let transactionUniqueId: string | undefined = undefined;
  let purposeOfPayment: string | undefined = undefined;
  let statementNarrative: string | undefined = undefined;
  let recipientCity: string | undefined;
  let recipientNames: string | undefined = beneficiaryData[0];
  let recipientNamesArray = beneficiaryData[0].split(' ');
  let recipientFirstName: string | undefined = recipientNamesArray[0];
  let recipientLastName: string | undefined = recipientNamesArray[recipientNamesArray.length - 1];
  let recipientAccountType: string | undefined = undefined;
  let recipientCountry: string | undefined;
  let recipientState: string | undefined;
  let recipientAddressLine1: string | undefined = beneficiaryData[1];
  let recipientAddressLine2: string | undefined = beneficiaryData.length === 4 ? beneficiaryData[2] : undefined;
  let recipientPostalCode: string | undefined;
  let recipientContactEmail: string | undefined = undefined;
  let recipientIdNumber: string | undefined = undefined;
  let recipientIdType: string | undefined = undefined;
  let recipientIdIssueCountry: string | undefined = undefined;
  let recipientContactNumber: string | undefined = undefined;
  let recipientContactNumberType: string | undefined = undefined;
  let recipientAccountNumber: string | undefined = data.BENEFICIARY_ACCOUNT_NUMBER; // Add random randomClientReferenceId because from frontend they do not send a unequal ClientReferenceId
  let bic: string | undefined = undefined;
  let branchCode: any | undefined = undefined;
  let bankCode: string | number | undefined = data.BANK_CODE ? data.BANK_CODE : undefined;
  let bankName: string | undefined = undefined;
  let bankAccountNumberType: string | undefined = undefined;
  let currencyCode: string | undefined = data.ORIGINAL_CURRENCY;
  let transactionAmount: number | undefined = data.AMOUNT_ORIGINAL_CURRENCY;
  let senderClientNumber: number | undefined = undefined; // Coming from orasys
  let senderNames: string | undefined = data.SENDER_NAME;
  let senderFirstName: string | undefined = undefined;
  let senderLastName: string | undefined = undefined;
  let senderAddressLine1: string | undefined = undefined;
  let senderAddressLine2: string | undefined = undefined;
  let senderPostalCode: string | undefined = undefined;
  let senderCity: string | undefined = undefined;
  let senderCountry: string | undefined = undefined;
  let senderState: string | undefined = undefined;
  let senderAccountNumber: string | undefined = undefined;
  let senderAccountType: string | undefined = undefined;
  let additionalData: VisaType.AdditionalData | undefined = undefined;
  let identificationList: VisaType.IdentificationList | undefined = undefined;
  let clientReferenceId: string | undefined = data.REFERENCE_ID;
  let countryCode: string | undefined = undefined;
  let bankAccountType: string | undefined = undefined;
  let initiatingPartyId: number | undefined = Number(process.env.VISA_INITIATING_PARTY_ID_RYVYL);

  // Reason parts
  const reasonParts = data?.REASON?.replace(`${senderNames} `, '').split(',') ?? [];
  // recipientAccountType = reasonParts[0];

  //set the type of the recipient based on the names.
  await classifyCustomerType(data.BENEFICIARY_NAME_AND_ADDRESS)
    .then((result) => {
      if (result == 'Person') {
        recipientAccountType = 'I';
      } else recipientAccountType = 'C';
    })
    .catch((error) => AIClassifierLogger.log('base', 'General Error'));

  //leave recipientNames undefined if the recipient is an individual
  if (recipientAccountType == 'I') {
    recipientNames = undefined;
  }
  //leave recipientNames undefined if the recipient is an individual
  if (recipientAccountType == 'C') {
    recipientFirstName = undefined;
    recipientLastName = undefined;
  }

  await classifyCustomerType(senderNames)
    .then((result) => {
      if (result == 'Person') {
        senderAccountType = 'I';
      } else senderAccountType = 'C';
    })
    .catch((error) => AIClassifierLogger.log('base', 'General Error'));

  // set sender data
  senderAccountNumber = data.SENDER_ACCOUNT_NUMBER;
  senderNames = senderData[0];
  senderAddressLine1 = senderData[1];
  const senderRegionDetails = senderData[1]?.split(',');
  senderCity = senderRegionDetails?.at(0)?.trim() ?? '';
  senderPostalCode = senderRegionDetails?.at(1)?.trim() ?? '';
  senderCountry = senderRegionDetails?.at(2)?.trim() ?? '';
  senderState = isFromState(data.SENDER_NAME) ? (senderRegionDetails?.at(3)?.trim() ?? '') : undefined;

  //
  //add the first and last name of the sender if they are an individual
  if (senderAccountType == 'I') {
    let senderNamesArray = senderNames.split(' ');
    senderFirstName = senderNamesArray[0];
    senderLastName = senderNamesArray[senderNamesArray.length - 1];
    senderNames = undefined;
  }

  //set beneficiary data
  recipientAddressLine1 = beneficiaryData[1];
  const beneficiaryRegionDetails = beneficiaryData[1]?.split(',');
  recipientCity = beneficiaryRegionDetails?.at(0)?.trim() ?? '';
  recipientPostalCode = beneficiaryRegionDetails?.at(1)?.trim() ?? '';
  recipientCountry = beneficiaryRegionDetails?.at(2)?.trim() ?? '';
  recipientState = isFromState(data.SENDER_NAME) ? (beneficiaryRegionDetails?.at(3)?.trim() ?? '') : undefined;

  countryCode = getCountryCodeByCurrency(data.ORIGINAL_CURRENCY);
  switch (countryCode) {
    //Phase 1
    case 'CAN': {
      recipientPostalCode = reasonParts.at(1)?.trim();
      recipientState = reasonParts.at(2)?.trim();
      branchCode = reasonParts.at(3)?.trim() ?? '';
      bankName = bankNameParts?.at(0)?.trim() ?? '';
      break;
    }

    //Phase 2
    case 'COL': {
      bankName = bankNameParts?.at(0)?.trim();
      recipientContactEmail = reasonParts.at(1)?.trim();
      recipientContactEmail = recipientContactEmail?.replace(' ', '@');
      recipientAccountNumber = recipientAccountNumber.replace(/-/g, '');

      recipientIdType = reasonParts.at(2)?.trim() ?? 'N';
      recipientIdNumber = reasonParts.at(3)?.trim();
      recipientIdIssueCountry = reasonParts.at(4)?.trim();

      bankAccountType = reasonParts.at(5)?.trim() || '1';
      break;
    }
    case 'IND': {
      bankCode = data.BANK_CODE ?? '';
      bankName = bankNameParts?.at(0);
      bankAccountType = reasonParts.at(1)?.trim() || '1';
      break;
    }
    case 'IDN': {
      bankAccountType = undefined;
      bic = data.BENEFICIARY_BIC; //we'll be using option 2, which requires BIC only.
      bankCode = undefined;
      branchCode = undefined;
      bankName = bankNameParts?.at(0)?.trim();
      break;
    }
    case 'PHL': {
      bankName = bankNameParts?.at(0);
      break;
    }
    case 'ZAF': {
      recipientContactNumber = reasonParts.at(1)?.trim();
      recipientContactEmail = reasonParts.at(2)?.trim() || '';
      branchCode = data.BANK_CODE;
      bankCode = undefined; // must be undefined for this country. This is the error i got when I tried sending it.
      bankName = bankNameParts?.at(0)?.trim();
      break;
    }
    case 'THA': {
      bic = data.BENEFICIARY_BIC;
      bankName = bankNameParts?.at(0);
      bankCode = undefined;
      bankAccountNumberType = undefined;
      recipientAccountNumber = recipientAccountNumber.replace(/-/g, '');
      break;
    }

    //Phase 3
    case 'ARE': {
      bankName = bankNameParts?.at(0);
      break;
    }
    case 'TUR': {
      // seems to be optional for individuals.
      recipientIdType = reasonParts.at(1)?.trim() ?? 'N';
      recipientIdNumber = reasonParts.at(2)?.trim();
      recipientIdIssueCountry = reasonParts.at(3)?.trim();

      bankName = bankNameParts?.at(0);
      break;
    }
    case 'MEX': {
      bankName = bankNameParts?.at(0);
      bankCode = undefined;
      break;
    }
    case 'BRA': {
      bankName = bankNameParts?.at(0)?.trim();

      recipientIdType = reasonParts.at(1)?.trim() ?? 'T';
      recipientIdNumber = reasonParts.at(2)?.trim();
      recipientIdIssueCountry = reasonParts.at(3)?.trim();

      bankCode = data.BANK_CODE;
      branchCode = reasonParts.at(4)?.trim() || '';

      bankAccountType = reasonParts.at(5)?.trim() || '1';
      break;
    }
    case 'GBR': {
      bankName = bankNameParts?.at(0);
      break;
    }
    case 'HKG': {
      bankCode = data.BANK_CODE;
      branchCode = reasonParts?.at(1)?.trim();
      bankName = bankNameParts?.at(0);
      break;
    }
  }
  return {
    transactionUniqueId,
    senderNames,
    senderFirstName,
    senderLastName,
    senderAddressLine1,
    senderAddressLine2,
    senderPostalCode,
    senderCity,
    senderCountry,
    senderState,
    senderAccountType,
    senderAccountNumber,
    recipientNames,
    recipientFirstName,
    recipientLastName,
    recipientAddressLine1,
    recipientAddressLine2,
    recipientPostalCode,
    recipientCity,
    recipientCountry,
    recipientState,
    recipientAccountType,
    recipientAccountNumber,
    recipientIdType,
    recipientIdNumber,
    recipientIdIssueCountry,
    recipientContactEmail,
    recipientContactNumber,
    recipientContactNumberType,
    bic,
    senderClientNumber,
    transactionAmount,
    bankName,
    branchCode,
    bankCode,
    currencyCode,
    purposeOfPayment,
    statementNarrative,
    bankAccountNumberType,
    additionalData,
    identificationList,
    clientReferenceId,
    countryCode,
    bankAccountType,
    initiatingPartyId
  };
}
export function extractPayloadDetailsFromOrasysPaxum(
  data: Transaction,
  paxumTransaction: PaxumVisaTransactionInterface
): VisaType.AllNeededFieldsForVisa {
  const paxumUniqueId = data?.REASON?.split(',')[0];
  if (!paxumUniqueId) {
    throw new Error('UniqueId is not provided!');
  }
  // let paxumTransaction: PaxumVisaTransactionDocument | null = null;
  // try {
  //   paxumTransaction = await findPaxumVisaTransactionByUniqueId(paxumUniqueId);
  //   if (!paxumTransaction) {
  //     throw new Error(`Paxum Visa Transaction does not have record with uniqueId = ${paxumUniqueId}`);
  //   }
  // } catch (error: any) {
  //   throw new Error(`${error.message}`);
  // }

  // Beneficiary bank name parts
  const bankNameParts = data.BENEFICIARY_BANK_NAME?.split('\r');

  let transactionUniqueId: string | undefined = paxumTransaction?.uniqueId;
  let purposeOfPayment: string | undefined = paxumTransaction?.additionalFields?.get('purposeOfPayment');
  let statementNarrative: string | undefined = undefined;
  let recipientCity: string | undefined = paxumTransaction?.additionalFields?.get('recipientCity');
  let recipientNames: string | undefined = paxumTransaction?.additionalFields?.get('recipientNames');
  let recipientFirstName: string | undefined = paxumTransaction?.additionalFields?.get('recipientFirstName');
  let recipientLastName: string | undefined = paxumTransaction?.additionalFields?.get('recipientLastName');
  let recipientAccountType: string | undefined = paxumTransaction?.recipientType;
  let recipientCountry: string | undefined = paxumTransaction?.additionalFields?.get('recipientCountry');
  let recipientState: string | undefined = paxumTransaction?.additionalFields?.get('recipientState');
  let recipientAddressLine1: string | undefined = paxumTransaction?.additionalFields?.get('recipientAddress1');
  let recipientAddressLine2: string | undefined = paxumTransaction?.additionalFields?.get('recipientAddress2');
  let recipientPostalCode: string | undefined = paxumTransaction?.additionalFields?.get('recipientPostCode');
  let recipientContactEmail: string | undefined = paxumTransaction?.additionalFields?.get('recipientContactEmail');
  let recipientIdNumber: string | undefined = paxumTransaction?.additionalFields?.get('recipientIdNumber');
  let recipientIdType: string | undefined = paxumTransaction?.additionalFields?.get('recipientIdType');
  let recipientIdIssueCountry: string | undefined = paxumTransaction?.additionalFields?.get('recipientIdIssueCountry');
  let recipientContactNumber: string | undefined = paxumTransaction?.additionalFields?.get('recipientContactNumber');
  let recipientContactNumberType: string | undefined =
    paxumTransaction?.additionalFields?.get('recipientContactNumberType');
  let recipientAccountNumber: string | undefined = data.BENEFICIARY_ACCOUNT_NUMBER; // Add random randomClientReferenceId because from frontend they do not send a unequal ClientReferenceId
  let bic: string | undefined = undefined;
  let branchCode: any | undefined = paxumTransaction?.additionalFields?.get('branchCode');
  let bankCode: string | number | undefined = paxumTransaction?.additionalFields?.get('bankCode');
  let bankName: string | undefined = bankNameParts?.at(0)?.trim();
  let bankAccountNumberType: string | undefined = undefined;
  let currencyCode: string | undefined = data.ORIGINAL_CURRENCY;
  let transactionAmount: number | undefined = data.AMOUNT_ORIGINAL_CURRENCY;
  let senderClientNumber: number | undefined = undefined; // Coming from orasys
  let senderNames: string | undefined = undefined;
  let senderFirstName: string | undefined = undefined;
  let senderLastName: string | undefined = undefined;
  let senderAddressLine1: string | undefined = undefined;
  let senderAddressLine2: string | undefined = undefined;
  let senderPostalCode: string | undefined = undefined;
  let senderCity: string | undefined = undefined;
  let senderCountry: string | undefined = undefined;
  let senderState: string | undefined = undefined;
  let senderAccountNumber: string | undefined = undefined;
  let senderAccountType: string | undefined = undefined;
  let additionalData: VisaType.AdditionalData | undefined = undefined;
  let identificationList: VisaType.IdentificationList | undefined = undefined;
  let clientReferenceId: string | undefined = data.REFERENCE_ID;
  let countryCode: string | undefined = undefined;
  let bankAccountType: string | undefined = undefined;

  //TODO - Add logic for using different Initiating Party ID for different senders
  let initiatingPartyId: number = Number(process.env.VISA_INITIATING_PARTY_ID_FUNDSTR);

  countryCode = getCountryCodeByCurrency(data.ORIGINAL_CURRENCY);
  switch (countryCode) {
    case 'THA': {
      bic = data.BENEFICIARY_BIC;
      break;
    }
  }
  return {
    transactionUniqueId,
    senderNames,
    senderFirstName,
    senderLastName,
    senderAddressLine1,
    senderAddressLine2,
    senderPostalCode,
    senderCity,
    senderCountry,
    senderState,
    senderAccountType,
    senderAccountNumber,
    recipientNames,
    recipientFirstName,
    recipientLastName,
    recipientAddressLine1,
    recipientAddressLine2,
    recipientPostalCode,
    recipientCity,
    recipientCountry,
    recipientState,
    recipientAccountType,
    recipientAccountNumber,
    recipientIdType,
    recipientIdNumber,
    recipientIdIssueCountry,
    recipientContactEmail,
    recipientContactNumber,
    recipientContactNumberType,
    bic,
    senderClientNumber,
    transactionAmount,
    bankName,
    branchCode,
    bankCode,
    currencyCode,
    purposeOfPayment,
    statementNarrative,
    bankAccountNumberType,
    additionalData,
    identificationList,
    clientReferenceId,
    countryCode,
    bankAccountType,
    initiatingPartyId
  };
}

export async function cancelVisaDirectTransactionFromElcoin(clientReferenceId: string) {
  try {
    let visaResult: any = await cancelVisaTransaction(clientReferenceId);

    // :TODO - to save transaction on orasys and mongodb (now we are do not save because do not have unique_id and reference_number)
    return {
      statusCode: 200,
      message: visaResult.message ? visaResult.message : 'Transaction successfully send to Visa for cancellation.'
    };
  } catch (error: any) {
    visaLogger.error('sendVisaDirectTransactionFromElcoin:', error.message);
    return { statusCode: error.statusCode ? error.statusCode : 400, message: error.message };
  }
}

export async function cancelVisaTransaction(clientReferenceId: string): Promise<VisaType.VisaTransactionResponse> {
  // :TODO to make logic for initiating party_id
  const initiatingPartyId = process.env.VISA_INITIATING_PARTY_ID_FUNDSTR;

  if (!initiatingPartyId) {
    visaLogger.error('initiatingPartyId is missing!');
    throw new Error('Something went wrong please try again!');
  }
  const cancelQueryData: VisaType.CancelQuery = {
    id: clientReferenceId,
    idType: VisaType.IdType.ClientReferenceId,
    initiatingPartyId: Number(initiatingPartyId)
  };

  const options = await getVisaDirectConfiguredOptions('CANCEL_TRANSACTION', 'DELETE', undefined, cancelQueryData);

  try {
    return await new Promise(async (resolve, reject) => {
      request.delete(options, async (err, response) => {
        if (err) {
          visaLogger.debug(`Error: ${err}`);
          return reject(new Error(err.message));
        }

        if (!response || !response.statusCode) {
          visaLogger.error('NO_RESPONSE_NO_STATUS_CODE');
          return reject(new Error('NO_RESPONSE_NO_STATUS_CODE'));
        }

        if (response.statusCode === 500) {
          const message = response.body?.errorResponse?.message ?? 'VISA_SERVER_ERROR';
          reject(new Error(message));
          return;
        }

        try {
          const decryptKey = await jose.JWK.asKey(fs.readFileSync(config.mlePrivateKeyPath), 'pem');
          const decryptedResult = await jose.JWE.createDecrypt(decryptKey).decrypt(response.body.encData);
          const decryptedData = JSON.parse(String(decryptedResult.plaintext));

          visaLogger.info(`VISA DIRECT STATUS CODE CANCEL TRANSACTION: ${response.statusCode}`);

          switch (response.statusCode) {
            case 200:
            case 202:
              visaLogger.info(
                `Transaction with reference id ${decryptedData?.transactionDetail?.clientReferenceId} successfully sent to Visa for cancellation, status of transaction ${decryptedData?.transactionDetail?.status}`
              );
              resolve({
                statusCode: response.statusCode,
                data: decryptedData
              });
              break;

            case 400:
              reject({
                message: { statusCode: 400, message: extractVisaErrorDetailsMessage(decryptedData.errorResponse) }
              });
              break;

            case 401:
            case 403:
            case 404:
              reject({
                message: {
                  statusCode: response.statusCode,
                  message: extractVisaErrorDetailsMessage(decryptedData.errorResponse)
                }
              });
              break;

            case 503:
              reject({
                message: {
                  statusCode: response.statusCode,
                  message: 'Service unavailable. Please try again later.'
                }
              });
              break;

            default:
              reject(
                new Error(
                  decryptedData?.errorResponse
                    ? JSON.stringify(decryptedData.errorResponse)
                    : `Unexpected status code: ${response.statusCode}`
                )
              );
          }
        } catch (decryptError) {
          visaLogger.error('Decryption failed', decryptError);
          reject(new Error('Decryption failed'));
        }
      });
    });
  } catch (error: any) {
    visaLogger.error(`UNEXPECTED_ERR_OCCURRED: ${JSON.stringify(error.message)}`);
    return Promise.reject(error.message);
  }
}
