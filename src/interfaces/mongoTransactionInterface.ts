import { Document } from 'mongoose';
import { Status, TransactionTypes } from './sumsubTransactionWebhook';

export interface MongoTransactionInterface extends Document {
  referenceNumber: string;
  referenceId: string;
  uniqueId: number;
  status: string;
  details: string;
  manualStatus: Status;
  amount: number;
  currencyCode: string;
  transactionType: TransactionTypes;
  amlInsertInOra: boolean;
}

export interface VisaDirectTransactionInterface extends MongoTransactionInterface {
  visaStatus: string;
}
