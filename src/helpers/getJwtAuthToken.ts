import jwt from 'jsonwebtoken';
import { Permission } from '../interfaces/permission';
import { getUserRole } from '../services/userService';
import { User } from '../interfaces/mongoUser';
import { JWT_ALGORITHM } from '../../submodules/ryvyl-commons/config';
import { setRyvylAdminIsBlocked } from '../../submodules/ryvyl-commons/services/redisService';
import { ObjectId } from 'mongoose';
import { JWT_EXPARATION_TIME } from '../config/config';

const getJwtAuthToken = async (user: User) => {
  const privateKeyENV = process.env.JWT_SECRET_KEY;
  if (!privateKeyENV) {
    throw new Error('JWT_SECRET_KEY is not defined');
  }

  const privateKey = privateKeyENV?.replace(/\\n/g, '\n');
  const role = await getUserRole(user.username);
  let permissions: Permission[] = [];
  let roleName = '';
  let roleIsActive = false;

  if (role) {
    roleName = role.name;
    roleIsActive = role.isActive;
    if (role.permissions?.length > 0) {
      permissions = role.permissions.map((perm) => ({
        action: perm.action,
        object: perm.object,
        isActive: perm.isActive
      }));
    }
  }

  // Set to redis cache the user is blocked status
  setRyvylAdminIsBlocked((user._id as ObjectId).toString(), user.isBlocked);

  return jwt.sign(
    {
      username: user.username,
      userId: user._id,
      role: roleName,
      roleIsActive,
      permissions
    },
    privateKey,
    {
      algorithm: JWT_ALGORITHM as jwt.Algorithm,
      expiresIn: JWT_EXPARATION_TIME as jwt.SignOptions['expiresIn']
    }
  );
};

export default getJwtAuthToken;
