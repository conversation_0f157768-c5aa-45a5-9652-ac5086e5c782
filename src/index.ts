import { Express } from 'express';
import http from 'http';
import dotenv from 'dotenv';
dotenv.config();
import { app, copApp, paxumApp, webhookApp } from './app';
import mongoose from './config/mongoose';
import { kafkaService } from '../submodules/ryvyl-commons/services/kafkaService';
import logger from '../submodules/ryvyl-commons/services/loggerService';
import redisClient from '../submodules/ryvyl-commons/services/redisService';

import { COP_PORT, PAXUM_TO_IL_PORT, PORT, WEBHOOK_PORT } from './config/config';
import {
  initVisaDirectBalancesCron,
  initVisaDirectCron,
  processResendJotformWebhookCron,
  startAMLCronJob
} from './services/cronService';
import { connectToOracle } from './config/oracledb';

mongoose.connectDB(() => afterConnect(app), false);

let server: http.Server | undefined;
let copServer: http.Server | undefined;
let webhookServer: http.Server | undefined;
let paxumToIlServer: http.Server | undefined;

app.on('ready', async () => {
  server = app.listen(PORT, () => logger.info(`Server is listening on port: ${PORT}`));
  copServer = copApp.listen(COP_PORT, () => logger.info(`CoP server is listening on port: ${COP_PORT}`));
  webhookServer = webhookApp.listen(WEBHOOK_PORT, () =>
    logger.info(`Webhook server is listening on port: ${WEBHOOK_PORT}`)
  );
  paxumToIlServer = paxumApp.listen(PAXUM_TO_IL_PORT, () =>
    logger.info(`PAXUM to IL server is listening on port: ${PAXUM_TO_IL_PORT}`)
  );
  await kafkaService.connect();
  await connectToOracle();
  if (process.env.VISA_DIRECT_BALANCES_ACTIVE === 'true') initVisaDirectBalancesCron();
  if (process.env.VISA_DIRECT_ACTIVE === 'true') initVisaDirectCron();
  if (process.env.JOTFORM_ACTIVE === 'true') processResendJotformWebhookCron();
  if (process.env.AML_ACTIVE === 'true') startAMLCronJob();

  try {
    await redisClient.connect();
  } catch (error) {
    logger.error(`❌ Redis connection error: ${error}`);
  }
});

process.on('SIGTERM', () => {
  closeServers('SIGTERM');
});

// To disconnect the server when we press 'ctrl + c' on the terminal
process.on('SIGINT', () => {
  closeServers('SIGINT');
});

function closeServers(signal: string) {
  logger.info(`Received signal "${signal}", shutting down...`);
  if (server) closeServer(server);
  if (copServer) closeServer(copServer);
  if (webhookServer) closeServer(webhookServer);
  if (paxumToIlServer) closeServer(paxumToIlServer);
  redisClient.quit();
  logger.info('Exiting process...');
  process.exit(0);
}

function closeServer(server: http.Server) {
  server.close(() => {
    logger.info('The server has been stopped.');
  });
  kafkaService.disconnect();
}

/**
 * Function to be executed after successful connection
 *
 * @param app The Express app
 */
async function afterConnect(app: Express): Promise<void> {
  app.emit('ready');
}
