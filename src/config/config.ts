export const getEnvironmentVariable = (name: string): string => {
  const env = process.env[name];
  if (!env) {
    throw new Error(`Couldn't find environment variable: ${name}`);
  }
  return env;
};

export const GITHUB = {
  TOKEN: process.env.GITHUB_TOKEN!!,
  REPO: process.env.GITHUB_REPO!!,
  REPO_OWNER: process.env.GITHUB_REPO_OWNER!!,
  DEFAULT_BRANCH: process.env.GITHUB_DEFAULT_TRACKED_BRANCH!!,
  DEVELOPMENT_BRANCH: process.env.GITHUB_DEV_TRACKED_BRANCH!!,
  COMMITS_COUNT: process.env.GITHUB_FETCHED_COMMITS_PER_PAGE!!
};

export const DB = {
  HOST: process.env.MONGODB_HOST || 'localhost',
  PORT: process.env.MONGODB_PORT || '27017',
  NAME: process.env.MONGODB_DATABASE_NAME || '',
  USERNAME: process.env.MONGODB_USERNAME || '',
  PASSWORD: process.env.MONGODB_PASSWORD || '',
  URI: process.env.MONGODB_URI || '',
  CA_CERT: process.env.CA_CERT
};

export const DEPLOYMENT = process.env.DEPLOYMENT!!;
export const HEALTH_CHECK = process.env.HEALTH_CHECK_LOAD_INTERVAL!!;

export const PORT = process.env.PORT || 5000;
export const WEBHOOK_PORT = process.env.WEBHOOK_PORT || 32550;
export const PAXUM_TO_IL_PORT = process.env.PAXUM_TO_IL_PORT || 54567;
export const COP_PORT = process.env.COP_PORT || 51245;

export const JWT_EXPARATION_TIME = process.env.JWT_EXPARATION_TIME || '2h';
