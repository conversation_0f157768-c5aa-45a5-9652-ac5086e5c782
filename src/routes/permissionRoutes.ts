import express from 'express';

import { createPermissionController, getAllPermissionsController } from '../controllers/permissionController';
import {
  createRoleController,
  getRolesController,
  getRoleByIdController,
  updateRoleController
} from '../controllers/roleController';
import {
  createPermissionSchema,
  createRoleSchema,
  updateRoleSchema
} from '../schemaValidation/permissionRoleSchemaValidation';

import validateRequest from '../../submodules/ryvyl-commons/middlewares/validateReqMiddleware';
import { permissionManagement } from '../../submodules/ryvyl-commons/middlewares/permissionManagement';
import { PermissionObject } from '../../submodules/ryvyl-commons/constants/permissionObject';
import { PermissionAction } from '../../submodules/ryvyl-commons/constants/permissionAction';
import { authenticateJWT } from '../../submodules/ryvyl-commons/middlewares/authMiddleware';
import isBlockedMiddleware from '../../submodules/ryvyl-commons/middlewares/adminUserIsBlocked';

const router = express.Router();

// Permission routes
router.post(
  '/permissions',
  authenticateJWT,
  isBlockedMiddleware,
  validateRequest(createPermissionSchema),
  createPermissionController
);

router.get('/permissions', authenticateJWT, isBlockedMiddleware, getAllPermissionsController);

// Role routes
router.post('/roles', authenticateJWT, isBlockedMiddleware, validateRequest(createRoleSchema), createRoleController);

router.get(
  '/roles',
  authenticateJWT,
  isBlockedMiddleware,
  permissionManagement([{ action: PermissionAction.READ, object: PermissionObject.ALL }]),
  getRolesController
);

router.get(
  '/roles/:id',
  authenticateJWT,
  isBlockedMiddleware,
  permissionManagement([{ action: PermissionAction.READ, object: PermissionObject.ALL }]),
  getRoleByIdController
);

router.put('/roles/:id', authenticateJWT, isBlockedMiddleware, validateRequest(updateRoleSchema), updateRoleController);

export default router;
