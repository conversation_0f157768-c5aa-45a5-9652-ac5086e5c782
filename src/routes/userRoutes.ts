import express from 'express';

import {
  createU<PERSON><PERSON><PERSON>roller,
  getUsersController,
  getUserByIdController,
  updateUser<PERSON>ontroller,
  resetPasswordController
} from '../controllers/user';
import {
  createUserSchema,
  updateUserSchema,
  getUsersQuerySchema,
  updatePasswordSchema
} from '../schemaValidation/userSchemaValidation';

import validateRequest from '../../submodules/ryvyl-commons/middlewares/validateReqMiddleware';
import { permissionManagement } from '../../submodules/ryvyl-commons/middlewares/permissionManagement';
import { PermissionObject } from '../../submodules/ryvyl-commons/constants/permissionObject';
import { PermissionAction } from '../../submodules/ryvyl-commons/constants/permissionAction';
import { authenticateJWT } from '../../submodules/ryvyl-commons/middlewares/authMiddleware';
import isBlockedMiddleware from '../../submodules/ryvyl-commons/middlewares/adminUserIsBlocked';

const router = express.Router();

// User routes
router.post(
  '/',
  authenticateJWT,
  isBlockedMiddleware,
  validateRequest(createUserSchema),
  permissionManagement([{ action: PermissionAction.CREATE, object: PermissionObject.ALL }]),
  createUserController
);

router.get(
  '/',
  authenticateJWT,
  isBlockedMiddleware,
  permissionManagement([{ action: PermissionAction.READ, object: PermissionObject.ALL }]),
  validateRequest(getUsersQuerySchema),
  getUsersController
);

router.get(
  '/:id',
  authenticateJWT,
  isBlockedMiddleware,
  permissionManagement([{ action: PermissionAction.READ, object: PermissionObject.ALL }]),
  getUserByIdController
);

router.put(
  '/:id',
  authenticateJWT,
  isBlockedMiddleware,
  validateRequest(updateUserSchema),
  permissionManagement([{ action: PermissionAction.UPDATE, object: PermissionObject.ALL }]),
  updateUserController
);

// updates user's own password
router.post(
  '/update-password',
  authenticateJWT,
  isBlockedMiddleware,
  validateRequest(updatePasswordSchema),
  updateUserController
);

// resets another user's password
router.post(
  '/:id/reset-password',
  authenticateJWT,
  isBlockedMiddleware,
  permissionManagement([{ action: PermissionAction.UPDATE, object: PermissionObject.ALL }]),
  resetPasswordController
);

export default router;
