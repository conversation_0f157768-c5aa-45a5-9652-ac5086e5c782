import express from 'express';
import { getInfo } from '../controllers/info';
import logger from '../../submodules/ryvyl-commons/services/loggerService';
import validateRequest from '../../submodules/ryvyl-commons/middlewares/validateReqMiddleware';
import {
  cancelVisaTransactionRequest,
  generateProofOfPaymentPDFByUniqueId,
  getAMLTransactions,
  getCompletedTransactionsByIdOrNumberController,
  getCompletedTransactionsByUniqueIdController,
  getVisaInvalidTransaction,
  getVisaInvalidTransactions,
  getVisaTransactions,
  rejectVisaInvalidTransaction,
  resendVisaTransaction,
  triggerManualAMLCheck
} from '../controllers/transactions';
import { verifyUser } from '../controllers/user';
import { getVisaBalances } from '../controllers/visaBalances';
import { getApplicantAdditionalInformation, sendSumsubTransaction } from '../controllers/sumsub';
import { getPaxumTransaction, getPaxumTransactions } from '../controllers/paxumController';
import { verifySumsubRyvylUsaToken } from '../middlewares/sumsubRyvylUsaToken';
import {
  getAuthorizedTransactionRequest,
  getErrorInboundTransactionRequest,
  getErrorOutboundTransactionRequest,
  getInboundTransactionRequest,
  getOutboundTransactionRequest,
  getSuccessfullyTransactionRequest,
  processInboundTransaction
} from '../controllers/baasController';
import { ipWhitelistMiddleware } from '../middlewares/whitelistingServers';
import { visaVmssMerchantMatch } from '../controllers/visaVmssController';
import { verifyTwoFactorTokenController, setupTwoFactorTokenController } from '../controllers/twoFactorController';
import { validateTwoFactorTokenSchema, setupTwoFactorTokenSchema } from '../schemaValidation/twoFactorSchemaValidation';
import { authenticateJWT } from '../../submodules/ryvyl-commons/middlewares/authMiddleware';
import permissionRoutes from './permissionRoutes';
import userRoutes from './userRoutes';
import isBlockedMiddleware from '../../submodules/ryvyl-commons/middlewares/adminUserIsBlocked';

// eslint-disable-next-line new-cap
const routes = express.Router();

// Health-check endpoint
routes.get('', function (_req: express.Request, res: express.Response) {
  logger.info(`Health-check probe/readiness: ${Date.now()}`);
  res.status(200).send('OK');
});

routes.get('/info', getInfo);

routes.post('/sign-in', verifyUser);

routes.get('/verify-token', authenticateJWT, isBlockedMiddleware, (_req: express.Request, res: express.Response) => {
  res.status(200).json({ valid: true });
});

// Add the permission routes
routes.use('/permissions-management', permissionRoutes);

// Add the user routes
routes.use('/users', userRoutes);

routes.post('/2fa/validate', validateRequest(validateTwoFactorTokenSchema), verifyTwoFactorTokenController);

routes.post('/2fa/setup', validateRequest(setupTwoFactorTokenSchema), setupTwoFactorTokenController);

routes.get('/transactions/ln', authenticateJWT, isBlockedMiddleware, getAMLTransactions);

routes.get(
  '/transaction/completed/unique-id/:uniqueId',
  authenticateJWT,
  isBlockedMiddleware,
  getCompletedTransactionsByUniqueIdController
);

routes.get(
  '/transaction/completed/by-id-or-number/:transactionId',
  authenticateJWT,
  isBlockedMiddleware,
  getCompletedTransactionsByIdOrNumberController
);

routes.get('/transactions/visa', authenticateJWT, isBlockedMiddleware, getVisaTransactions);
routes.get('/transactions/visa/invalid-payouts', authenticateJWT, isBlockedMiddleware, getVisaInvalidTransactions);
routes.get(
  '/transactions/visa/invalid-payouts/:referenceId',
  authenticateJWT,
  isBlockedMiddleware,
  getVisaInvalidTransaction
);

routes.get('/transactions/paxum', authenticateJWT, isBlockedMiddleware, getPaxumTransactions);
routes.get('/transactions/paxum/:uniqueId', authenticateJWT, isBlockedMiddleware, getPaxumTransaction);

routes.get('/account-balances', authenticateJWT, isBlockedMiddleware, getVisaBalances);

routes.post('/resend-visa-transfer', authenticateJWT, isBlockedMiddleware, resendVisaTransaction);
routes.post('/reject-visa-transfer', authenticateJWT, isBlockedMiddleware, rejectVisaInvalidTransaction);

routes.post('/check/transaction/sumsub', verifySumsubRyvylUsaToken, sendSumsubTransaction);
routes.post('/transactions/insert-inbound-transaction-in-orasys', ipWhitelistMiddleware, processInboundTransaction);

routes.delete(
  '/transactions/visa/:clientReferenceId',
  authenticateJWT,
  isBlockedMiddleware,
  cancelVisaTransactionRequest
);

routes.get('/baas/get-inbound-transactions/:uniqueId', ipWhitelistMiddleware, getInboundTransactionRequest);
routes.get('/baas/get-outbound-transactions/:uniqueId', ipWhitelistMiddleware, getOutboundTransactionRequest);
routes.get('/baas/get-successfully-transactions/:uniqueId', ipWhitelistMiddleware, getSuccessfullyTransactionRequest);
routes.get('/baas/get-authorized-transactions/:uniqueId', ipWhitelistMiddleware, getAuthorizedTransactionRequest);
routes.get('/baas/get-error-inbound-transactions/:uniqueId', ipWhitelistMiddleware, getErrorInboundTransactionRequest);
routes.get(
  '/baas/get-error-outbound-transactions/:uniqueId',
  ipWhitelistMiddleware,
  getErrorOutboundTransactionRequest
);

// Job Form
routes.get('/applicant/additional-information/:applicantId', ipWhitelistMiddleware, getApplicantAdditionalInformation);

routes.get(
  '/proof-of-payment/pdf/:uniqueId',
  authenticateJWT,
  isBlockedMiddleware,
  generateProofOfPaymentPDFByUniqueId
);

// Visa VMSS from
routes.post('/visa-vmss/merchant-match', ipWhitelistMiddleware, visaVmssMerchantMatch);

routes.post('/transactions/aml/check-manual', ipWhitelistMiddleware, authenticateJWT, triggerManualAMLCheck);

export default routes;
