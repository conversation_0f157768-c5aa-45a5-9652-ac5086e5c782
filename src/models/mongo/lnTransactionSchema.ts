import mongoose, { Schema } from 'mongoose';
import paginate from 'mongoose-paginate-v2';
import { MongoTransactionInterface } from '../../interfaces/mongoTransactionInterface';
import { Status, TransactionTypes } from '../../interfaces/sumsubTransactionWebhook';

const LNTransactionsSchema: Schema = new Schema<MongoTransactionInterface>(
  {
    referenceNumber: { type: String, required: true, unique: false, index: true },
    uniqueId: { type: Number, required: true, index: true },
    referenceId: { type: String, required: true, index: true },
    status: { type: String, required: false, unique: false },
    details: { type: String, required: false, unique: false },
    manualStatus: { type: String, enum: Object.values(Status), required: false },
    transactionType: { type: String, enum: Object.values(TransactionTypes), required: false },
    amount: { type: Number, required: false },
    currencyCode: { type: String, required: false },
    amlInsertInOra: { type: Boolean, required: false, default: false }
  },
  { timestamps: true }
);

LNTransactionsSchema.plugin(paginate);
interface LNTransactionsDocument extends mongoose.Document, MongoTransactionInterface {}

const LnTransaction = mongoose.model<LNTransactionsDocument, mongoose.PaginateModel<LNTransactionsDocument>>(
  'LNTransaction',
  LNTransactionsSchema
);

export default LnTransaction;
